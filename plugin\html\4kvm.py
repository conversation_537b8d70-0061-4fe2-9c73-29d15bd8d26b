# -*- coding: utf-8 -*-
# 4kvm - https://www.4kvm.net/
import re
import sys
import json
import time
from urllib.parse import quote, unquote
from pyquery import PyQuery as pq
sys.path.append('..')
from base.spider import Spider

class Spider(Spider):
    def init(self, extend=""):
        pass

    def getName(self):
        return "4kvm"

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def destroy(self):
        pass

    host = 'https://www.4kvm.net'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    def homeContent(self, filter):
        """获取首页内容和分类"""
        try:
            response = self.fetch_with_encoding(self.host, headers=self.headers)
            doc = self.getpq(response.text)
            
            result = {}
            classes = []
            
            # 获取分类导航 - 常见的分类链接模式
            nav_selectors = [
                'a[href*="/category/"]',
                'a[href*="/type/"]', 
                'a[href*="/t/"]',
                '.nav a',
                '.navbar a',
                '.menu a'
            ]
            
            nav_items = None
            for selector in nav_selectors:
                nav_items = doc(selector)
                if nav_items and len(nav_items) > 0:
                    self.log(f"找到分类导航: {selector}")
                    break
            
            if nav_items:
                for item in nav_items.items():
                    href = item.attr('href') or ''
                    text = self.fix_encoding(item.text().strip())
                    
                    if href and text and text not in ['首页', 'Home', '']:
                        # 提取分类ID
                        type_id = ''
                        if '/category/' in href:
                            type_id = href.split('/category/')[-1].rstrip('/')
                        elif '/type/' in href:
                            type_id = href.split('/type/')[-1].rstrip('/')
                        elif '/t/' in href:
                            type_id = href.split('/t/')[-1].rstrip('/')
                        
                        if type_id:
                            classes.append({
                                'type_name': text,
                                'type_id': type_id
                            })
            
            # 获取首页视频列表 - 尝试多种常见的视频容器选择器
            videos = []
            video_selectors = [
                '.module-item',
                '.video-item', 
                '.movie-item',
                '.card',
                '.item',
                'a[href*="/detail/"]',
                'a[href*="/video/"]',
                'a[href*="/play/"]'
            ]
            
            video_items = None
            for selector in video_selectors:
                video_items = doc(selector)
                if video_items and len(video_items) > 0:
                    self.log(f"找到视频容器: {selector} ({len(video_items)}个)")
                    break
            
            if video_items:
                seen_ids = set()  # 用于去重
                
                for item in video_items.items():
                    try:
                        video_data = self.parse_video_item(item, 'home')
                        if video_data and video_data['vod_id'] not in seen_ids:
                            seen_ids.add(video_data['vod_id'])
                            videos.append(video_data)
                    except Exception as e:
                        self.log(f"解析视频项时出错: {e}")
                        continue
            
            result['class'] = classes
            result['list'] = videos
            self.log(f"首页解析完成: {len(classes)}个分类, {len(videos)}个视频")
            return result
            
        except Exception as e:
            self.log(f"获取首页内容时出错: {e}")
            return {'class': [], 'list': []}

    def parse_video_item(self, item, page_type="category"):
        """解析视频项数据"""
        try:
            # 获取链接和标题
            href = ''
            title = ''
            
            if item.is_('a'):
                # 如果item本身是链接
                href = item.attr('href') or ''
                title = item.attr('title') or ''
            else:
                # 查找内部链接
                link_selectors = [
                    'a[href*="/detail/"]',
                    'a[href*="/video/"]', 
                    'a[href*="/play/"]',
                    'a'
                ]
                
                detail_link = None
                for selector in link_selectors:
                    detail_link = item.find(selector).eq(0)
                    if detail_link and detail_link.attr('href'):
                        break
                
                if detail_link:
                    href = detail_link.attr('href') or ''
                    title = detail_link.attr('title') or ''
            
            if not href:
                return None
            
            # 提取视频ID
            vod_id = ''
            if '/detail/' in href:
                vod_id = href.split('/detail/')[-1].rstrip('/')
            elif '/video/' in href:
                vod_id = href.split('/video/')[-1].rstrip('/')
            elif '/play/' in href:
                vod_id = href.split('/play/')[-1].split('-')[0]
            else:
                # 尝试从URL末尾提取数字ID
                match = re.search(r'/(\d+)/?$', href)
                if match:
                    vod_id = match.group(1)
            
            if not vod_id:
                return None
            
            # 获取标题 - 多种方式尝试
            if not title:
                # 从图片alt属性获取
                img_elem = item.find('img')
                if img_elem:
                    title = self.fix_encoding(img_elem.attr('alt') or '')
                
                # 从文本内容获取
                if not title:
                    title = self.fix_encoding(item.text().strip())
                    # 过滤无效标题
                    if title in ['正片', '详情', '播放', '观看', '']:
                        title = ''
            
            if not title:
                return None
            
            title = self.fix_encoding(title)
            
            # 获取图片 - 优先获取真实图片URL，避免占位图
            pic = ''
            img_elem = item.find('img')
            if img_elem:
                pic = img_elem.attr('data-original') or img_elem.attr('data-src') or img_elem.attr('src') or ''
                if pic and not pic.startswith('http'):
                    pic = self.host + pic if pic.startswith('/') else ''
            
            # 获取备注信息
            remarks = ''
            note_selectors = ['.module-item-note', '.note', '.remarks', '.status', '.year']
            for selector in note_selectors:
                note_elem = item.find(selector)
                if note_elem:
                    remarks = self.fix_encoding(note_elem.text().strip())
                    break
            
            return {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'vod_year': '',
                'vod_remarks': remarks
            }
            
        except Exception as e:
            self.log(f"解析视频项数据时出错: {e}")
            return None

    def homeVideoContent(self):
        """获取推荐视频"""
        return {'list': []}

    def categoryContent(self, tid, pg, filter, extend):
        """获取分类内容"""
        try:
            # 构建分类URL - 尝试多种常见格式
            url_patterns = [
                f"{self.host}/category/{tid}/",
                f"{self.host}/type/{tid}/",
                f"{self.host}/t/{tid}/",
                f"{self.host}/list/{tid}/",
            ]
            
            # 添加分页参数
            if int(pg) > 1:
                url_patterns = [
                    f"{self.host}/category/{tid}/page/{pg}/",
                    f"{self.host}/type/{tid}/page/{pg}/",
                    f"{self.host}/t/{tid}/page/{pg}/",
                    f"{self.host}/list/{tid}/page/{pg}/",
                    f"{self.host}/category/{tid}/?page={pg}",
                    f"{self.host}/type/{tid}/?page={pg}",
                ]
            
            # 尝试每个URL格式
            response = None
            for url in url_patterns:
                try:
                    response = self.fetch_with_encoding(url, headers=self.headers)
                    if response.status_code == 200:
                        self.log(f"分类URL成功: {url}")
                        break
                except:
                    continue
            
            if not response:
                self.log("所有分类URL格式都失败")
                return {'list': []}
            
            doc = self.getpq(response.text)
            
            # 获取视频列表
            videos = []
            video_selectors = [
                '.module-item',
                '.video-item', 
                '.movie-item',
                '.card',
                '.item'
            ]
            
            video_items = None
            for selector in video_selectors:
                video_items = doc(selector)
                if video_items and len(video_items) > 0:
                    break
            
            if video_items:
                seen_ids = set()  # 用于去重
                
                for item in video_items.items():
                    try:
                        video_data = self.parse_video_item(item, 'category')
                        if video_data and video_data['vod_id'] not in seen_ids:
                            seen_ids.add(video_data['vod_id'])
                            videos.append(video_data)
                    except Exception as e:
                        self.log(f"解析分类视频项时出错: {e}")
                        continue
            
            self.log(f"分类 {tid} 第 {pg} 页解析完成: {len(videos)}个视频")
            return {'list': videos}
            
        except Exception as e:
            self.log(f"获取分类内容时出错: {e}")
            return {'list': []}

    def detailContent(self, ids):
        """获取视频详情"""
        try:
            vod_id = ids[0]

            # 构建详情页URL - 尝试多种格式
            url_patterns = [
                f"{self.host}/detail/{vod_id}/",
                f"{self.host}/video/{vod_id}/",
                f"{self.host}/play/{vod_id}/",
                f"{self.host}/movie/{vod_id}/",
            ]

            response = None
            for url in url_patterns:
                try:
                    response = self.fetch_with_encoding(url, headers=self.headers)
                    if response.status_code == 200:
                        self.log(f"详情页URL成功: {url}")
                        break
                except:
                    continue

            if not response:
                self.log("所有详情页URL格式都失败")
                return {'list': []}

            doc = self.getpq(response.text)

            # 获取标题
            title = ''
            title_selectors = ['h1', '.title', '.video-title', '.movie-title', '.name']
            for selector in title_selectors:
                title_elem = doc(selector)
                if title_elem:
                    title = self.fix_encoding(title_elem.text().strip())
                    if title:
                        break

            # 获取封面图片
            pic = ''
            img_selectors = ['.video-pic img', '.movie-pic img', '.poster img', 'img']
            for selector in img_selectors:
                img_elem = doc(selector)
                if img_elem:
                    pic = img_elem.attr('data-original') or img_elem.attr('data-src') or img_elem.attr('src') or ''
                    if pic:
                        if not pic.startswith('http'):
                            pic = self.host + pic if pic.startswith('/') else ''
                        break

            # 获取视频信息
            content = ''
            info_selectors = ['.video-info', '.movie-info', '.content', '.description', '.intro']
            for selector in info_selectors:
                info_elem = doc(selector)
                if info_elem:
                    content = self.fix_encoding(info_elem.text().strip())
                    if content:
                        break

            # 获取演员和导演信息
            actor = ''
            director = ''

            # 尝试从信息区域提取
            info_items = doc('.info-item, .video-info-item, .movie-info-item')
            for item in info_items.items():
                item_text = self.fix_encoding(item.text())
                if '演员' in item_text or '主演' in item_text:
                    actor = item_text.split('：')[-1].strip() if '：' in item_text else ''
                elif '导演' in item_text:
                    director = item_text.split('：')[-1].strip() if '：' in item_text else ''

            # 获取播放源和播放列表
            play_from = []
            play_url = []

            # 查找播放源标签
            tab_selectors = ['.play-source .tab', '.source-tab', '.play-tab', '.tab-item']
            play_tabs = None
            for selector in tab_selectors:
                play_tabs = doc(selector)
                if play_tabs and len(play_tabs) > 0:
                    break

            # 查找播放列表
            list_selectors = ['.play-list', '.episode-list', '.playlist', '.play-content']
            play_lists = None
            for selector in list_selectors:
                play_lists = doc(selector)
                if play_lists and len(play_lists) > 0:
                    break

            if play_tabs and play_lists:
                for i, tab in enumerate(play_tabs.items()):
                    source_name = self.fix_encoding(tab.text().strip())
                    if source_name:
                        play_from.append(source_name)

                        # 获取对应的播放列表
                        episodes = []
                        if i < len(play_lists):
                            episode_items = play_lists.eq(i).find('a')
                            for ep in episode_items.items():
                                ep_title = self.fix_encoding(ep.text().strip())
                                ep_href = ep.attr('href')
                                if ep_title and ep_href:
                                    episodes.append(f"{ep_title}${ep_href}")

                        play_url.append('#'.join(episodes))

            # 如果没有找到播放源，尝试简单的链接列表
            if not play_from:
                episode_links = doc('a[href*="/play/"]')
                if episode_links:
                    play_from.append('默认播放源')
                    episodes = []
                    for ep in episode_links.items():
                        ep_title = self.fix_encoding(ep.text().strip())
                        ep_href = ep.attr('href')
                        if ep_title and ep_href:
                            episodes.append(f"{ep_title}${ep_href}")
                    play_url.append('#'.join(episodes))

            video_info = {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'vod_actor': actor,
                'vod_director': director,
                'vod_content': content,
                'vod_play_from': '$$$'.join(play_from),
                'vod_play_url': '$$$'.join(play_url)
            }

            self.log(f"详情页解析完成: {title}")
            return {'list': [video_info]}

        except Exception as e:
            self.log(f"获取视频详情时出错: {e}")
            return {'list': []}

    def searchContent(self, key, quick, pg):
        """搜索功能"""
        try:
            # 构建搜索URL - 尝试多种常见格式
            search_patterns = [
                f"{self.host}/search?wd={quote(key)}",
                f"{self.host}/search/?wd={quote(key)}",
                f"{self.host}/s/?wd={quote(key)}",
                f"{self.host}/search.php?wd={quote(key)}",
                f"{self.host}/index.php/search?wd={quote(key)}",
            ]

            # 添加分页参数
            if int(pg) > 1:
                search_patterns = [
                    f"{self.host}/search?wd={quote(key)}&page={pg}",
                    f"{self.host}/search/?wd={quote(key)}&page={pg}",
                    f"{self.host}/s/?wd={quote(key)}&page={pg}",
                ]

            response = None
            for search_url in search_patterns:
                try:
                    response = self.fetch_with_encoding(search_url, headers=self.headers)
                    if response.status_code == 200:
                        self.log(f"搜索URL成功: {search_url}")
                        break
                except:
                    continue

            if not response:
                self.log("所有搜索URL格式都失败")
                return {'list': []}

            doc = self.getpq(response.text)

            # 获取搜索结果
            videos = []
            video_selectors = [
                '.module-item',
                '.search-item',
                '.video-item',
                '.movie-item',
                '.result-item',
                '.card'
            ]

            video_items = None
            for selector in video_selectors:
                video_items = doc(selector)
                if video_items and len(video_items) > 0:
                    break

            if video_items:
                seen_ids = set()  # 用于去重

                for item in video_items.items():
                    try:
                        video_data = self.parse_video_item(item, 'search')
                        if video_data and video_data['vod_id'] not in seen_ids:
                            seen_ids.add(video_data['vod_id'])
                            videos.append(video_data)
                    except Exception as e:
                        self.log(f"解析搜索结果项时出错: {e}")
                        continue

            self.log(f"搜索 '{key}' 第 {pg} 页完成: {len(videos)}个结果")
            return {'list': videos}

        except Exception as e:
            self.log(f"搜索时出错: {e}")
            return {'list': []}

    def playerContent(self, flag, id, vipFlags):
        """获取播放地址"""
        try:
            # 播放页面URL
            play_url = f"{self.host}{id}" if id.startswith('/') else f"{self.host}/play/{id}"

            response = self.fetch_with_encoding(play_url, headers=self.headers)
            doc = self.getpq(response.text)

            # 查找播放器配置 - 常见的视频URL提取方式
            video_url = ''

            # 方法1: 从script标签中提取
            scripts = doc('script')
            for script in scripts.items():
                script_text = script.text()
                if script_text and ('mp4' in script_text or 'm3u8' in script_text):
                    # 提取视频URL
                    url_patterns = [
                        r'"url"\s*:\s*"([^"]+)"',
                        r"'url'\s*:\s*'([^']+)'",
                        r'src\s*:\s*"([^"]+)"',
                        r"src\s*:\s*'([^']+)'",
                        r'https?://[^"\s]+\.(?:mp4|m3u8)[^"\s]*'
                    ]

                    for pattern in url_patterns:
                        match = re.search(pattern, script_text)
                        if match:
                            video_url = match.group(1) if match.groups() else match.group(0)
                            break

                    if video_url:
                        break

            # 方法2: 从video标签中提取
            if not video_url:
                video_elem = doc('video source, video')
                if video_elem:
                    video_url = video_elem.attr('src') or ''

            # 方法3: 从iframe中提取
            if not video_url:
                iframe_elem = doc('iframe')
                if iframe_elem:
                    iframe_src = iframe_elem.attr('src') or ''
                    if iframe_src:
                        video_url = iframe_src

            self.log(f"播放地址解析: {video_url}")
            return {
                'parse': 0,
                'playUrl': '',
                'url': video_url
            }

        except Exception as e:
            self.log(f"获取播放地址时出错: {e}")
            return {
                'parse': 0,
                'playUrl': '',
                'url': ''
            }

    def localProxy(self, param):
        pass

    def fix_encoding(self, text):
        """修复UTF-8编码问题"""
        if not text:
            return text

        try:
            # 检查是否包含乱码特征（常见的UTF-8乱码模式）
            garbled_patterns = [
                '\u00e4\u00b8', '\u00e5', '\u00e6', '\u00e7', '\u00e8', '\u00e9',  # 常见乱码前缀
                '\u00c3\u00a4', '\u00c3\u00a5', '\u00c3\u00a6',  # UTF-8被误解为Latin1
                '\u00ef\u00bc', '\u00e2\u0080'  # 标点符号乱码
            ]

            has_garbled = any(pattern in text for pattern in garbled_patterns)

            if has_garbled:
                self.log("检测到编码问题，尝试修复...")

                # 方法1: 尝试Latin1->UTF-8转换
                try:
                    fixed = text.encode('latin1').decode('utf-8')
                    # 检查是否修复成功（包含中文字符）
                    if re.search(r'[\u4e00-\u9fff]', fixed):
                        self.log("使用Latin1->UTF-8修复成功")
                        return fixed
                except Exception as e:
                    self.log(f"Latin1->UTF-8修复失败: {e}")

                # 方法2: 尝试其他编码转换
                encodings = ['cp1252', 'iso-8859-1']
                for encoding in encodings:
                    try:
                        fixed = text.encode(encoding).decode('utf-8')
                        if re.search(r'[\u4e00-\u9fff]', fixed):
                            self.log(f"使用{encoding}->UTF-8修复成功")
                            return fixed
                    except:
                        continue

                self.log("编码修复失败，返回原文本")

            return text

        except Exception as e:
            self.log(f"编码修复异常: {e}")
            return text

    def fetch_with_encoding(self, url, **kwargs):
        """带编码处理的请求方法"""
        try:
            response = self.fetch(url, **kwargs)
            # 确保使用UTF-8编码
            response.encoding = 'utf-8'
            return response
        except Exception as e:
            self.log(f"请求失败: {e}")
            raise

    def getpq(self, text):
        """安全的pyquery解析"""
        try:
            return pq(text)
        except Exception as e:
            self.log(f"pyquery解析出错: {e}")
            try:
                return pq(text.encode('utf-8'))
            except:
                return pq('')
