# -*- coding: utf-8 -*-
# 4kvm插件基础结构测试
import sys
import os
sys.path.append('..')

def test_plugin_structure():
    """测试插件基础结构"""
    print("=" * 50)
    print("4kvm插件基础结构测试")
    print("=" * 50)
    
    try:
        # 测试导入
        print("1. 测试插件导入...")
        from kvm4k import Spider
        print("✅ 插件导入成功")
        
        # 测试实例化
        print("2. 测试插件实例化...")
        spider = Spider()
        print("✅ 插件实例化成功")
        
        # 测试初始化
        print("3. 测试插件初始化...")
        spider.init()
        print("✅ 插件初始化成功")
        
        # 测试基本属性
        print("4. 测试基本属性...")
        print(f"   插件名称: {spider.getName()}")
        print(f"   网站地址: {spider.host}")
        print("✅ 基本属性正常")
        
        # 测试方法存在性
        print("5. 测试必要方法存在性...")
        required_methods = [
            'homeContent',
            'categoryContent', 
            'detailContent',
            'searchContent',
            'playerContent',
            'fix_encoding',
            'fetch_with_encoding'
        ]
        
        for method_name in required_methods:
            if hasattr(spider, method_name):
                print(f"   ✅ {method_name} 方法存在")
            else:
                print(f"   ❌ {method_name} 方法缺失")
        
        # 测试编码修复函数
        print("6. 测试编码修复函数...")
        test_texts = [
            "正常中文文本",
            "",
            None,
            "English text",
            "测试\u00e4\u00b8\u00ad\u00e6\u0096\u0087"  # 模拟乱码
        ]
        
        for text in test_texts:
            try:
                result = spider.fix_encoding(text)
                print(f"   输入: {repr(text)} -> 输出: {repr(result)}")
            except Exception as e:
                print(f"   编码修复出错: {e}")
        
        print("✅ 编码修复函数测试完成")
        
        # 测试pyquery解析
        print("7. 测试pyquery解析...")
        test_html = "<html><body><h1>测试</h1></body></html>"
        try:
            doc = spider.getpq(test_html)
            title = doc('h1').text()
            print(f"   解析结果: {title}")
            print("✅ pyquery解析正常")
        except Exception as e:
            print(f"   pyquery解析出错: {e}")
        
        print("\n" + "=" * 50)
        print("基础结构测试完成")
        print("=" * 50)
        
    except ImportError as e:
        print(f"❌ 导入插件失败: {e}")
        print("请确保在plugin/html目录下运行此脚本")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def test_url_patterns():
    """测试URL模式识别"""
    print("\n" + "=" * 50)
    print("URL模式测试")
    print("=" * 50)
    
    try:
        from kvm4k import Spider
        spider = Spider()
        
        # 测试视频ID提取
        test_urls = [
            "/detail/12345/",
            "/video/67890/", 
            "/play/11111-1-1/",
            "/movie/22222/",
            "https://www.4kvm.net/detail/33333/",
            "/some/path/44444/"
        ]
        
        print("测试视频ID提取:")
        for url in test_urls:
            # 模拟parse_video_item中的ID提取逻辑
            vod_id = ''
            if '/detail/' in url:
                vod_id = url.split('/detail/')[-1].rstrip('/')
            elif '/video/' in url:
                vod_id = url.split('/video/')[-1].rstrip('/')
            elif '/play/' in url:
                vod_id = url.split('/play/')[-1].split('-')[0]
            else:
                import re
                match = re.search(r'/(\d+)/?$', url)
                if match:
                    vod_id = match.group(1)
            
            print(f"   {url} -> ID: {vod_id}")
        
        print("✅ URL模式测试完成")
        
    except Exception as e:
        print(f"❌ URL模式测试出错: {e}")

if __name__ == "__main__":
    test_plugin_structure()
    test_url_patterns()
