# -*- coding: utf-8 -*-
# 4kvm插件测试脚本
import sys
import os
sys.path.append('..')

def test_spider():
    """测试4kvm插件的基本功能"""
    try:
        from kvm4k import Spider
        
        spider = Spider()
        spider.init()
        
        print("=" * 50)
        print("4kvm插件功能测试")
        print("=" * 50)
        
        # 测试首页内容
        print("\n=== 测试首页内容 ===")
        try:
            home_result = spider.homeContent({})
            classes = home_result.get('class', [])
            videos = home_result.get('list', [])
            
            print(f"分类数量: {len(classes)}")
            if classes:
                print("前5个分类:")
                for i, cls in enumerate(classes[:5]):
                    print(f"  {i+1}. {cls['type_name']} (ID: {cls['type_id']})")
            
            print(f"首页视频数量: {len(videos)}")
            if videos:
                print("前3个视频:")
                for i, video in enumerate(videos[:3]):
                    print(f"  {i+1}. {video['vod_name']} (ID: {video['vod_id']})")
                    print(f"      封面: {video['vod_pic'][:50]}..." if video['vod_pic'] else "      封面: 无")
                    print(f"      备注: {video['vod_remarks']}")
        except Exception as e:
            print(f"首页测试失败: {e}")
        
        # 测试分类内容
        print("\n=== 测试分类内容 ===")
        try:
            if classes:
                first_category = classes[0]
                category_result = spider.categoryContent(first_category['type_id'], '1', {}, {})
                category_videos = category_result.get('list', [])
                
                print(f"分类 '{first_category['type_name']}' 视频数量: {len(category_videos)}")
                if category_videos:
                    print("前3个视频:")
                    for i, video in enumerate(category_videos[:3]):
                        print(f"  {i+1}. {video['vod_name']} (ID: {video['vod_id']})")
        except Exception as e:
            print(f"分类测试失败: {e}")
        
        # 测试搜索功能
        print("\n=== 测试搜索功能 ===")
        try:
            search_result = spider.searchContent('电影', False, '1')
            search_videos = search_result.get('list', [])
            
            print(f"搜索 '电影' 结果数量: {len(search_videos)}")
            if search_videos:
                print("前3个搜索结果:")
                for i, video in enumerate(search_videos[:3]):
                    print(f"  {i+1}. {video['vod_name']} (ID: {video['vod_id']})")
        except Exception as e:
            print(f"搜索测试失败: {e}")
        
        # 测试详情页面
        print("\n=== 测试详情页面 ===")
        try:
            if videos:
                first_video = videos[0]
                detail_result = spider.detailContent([first_video['vod_id']])
                
                if detail_result.get('list'):
                    detail = detail_result['list'][0]
                    print(f"视频标题: {detail.get('vod_name', '')}")
                    print(f"演员: {detail.get('vod_actor', '')}")
                    print(f"导演: {detail.get('vod_director', '')}")
                    print(f"简介: {detail.get('vod_content', '')[:100]}..." if detail.get('vod_content') else "简介: 无")
                    
                    play_from = detail.get('vod_play_from', '')
                    play_url = detail.get('vod_play_url', '')
                    
                    if play_from:
                        sources = play_from.split('$$$')
                        print(f"播放源数量: {len(sources)}")
                        print(f"播放源: {', '.join(sources)}")
                    
                    if play_url:
                        episodes = play_url.split('$$$')[0].split('#') if play_url else []
                        print(f"第一个播放源集数: {len(episodes)}")
                else:
                    print("详情页面解析失败")
        except Exception as e:
            print(f"详情测试失败: {e}")
        
        print("\n=== 测试完成 ===")
        
    except ImportError as e:
        print(f"导入插件失败: {e}")
        print("请确保在plugin/html目录下运行此脚本")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

def test_data_quality():
    """数据质量检查测试"""
    try:
        from kvm4k import Spider
        
        spider = Spider()
        spider.init()
        
        print("\n" + "=" * 50)
        print("4kvm插件数据质量检查")
        print("=" * 50)
        
        # 去重检查
        print("\n=== 去重检查 ===")
        home_result = spider.homeContent({})
        videos = home_result.get('list', [])
        video_ids = [v['vod_id'] for v in videos]
        unique_ids = set(video_ids)
        
        print(f"视频总数: {len(videos)}")
        print(f"唯一ID数: {len(unique_ids)}")
        print(f"重复数量: {len(video_ids) - len(unique_ids)}")
        
        if len(video_ids) == len(unique_ids):
            print("✅ 去重检查通过")
        else:
            print("❌ 存在重复视频")
            from collections import Counter
            duplicates = [vid for vid, count in Counter(video_ids).items() if count > 1]
            print(f"重复ID: {duplicates}")
        
        # 搜索结果质量检查
        print("\n=== 搜索结果质量检查 ===")
        search_result = spider.searchContent('测试', False, '1')
        search_videos = search_result.get('list', [])
        
        # 检查标题质量
        invalid_titles = []
        for video in search_videos:
            if video['vod_name'] in ['正片', '详情', '播放', '观看']:
                invalid_titles.append(video['vod_name'])
        
        if not invalid_titles:
            print("✅ 搜索结果标题质量检查通过")
        else:
            print(f"❌ 发现无效标题: {invalid_titles}")
        
        # 编码问题检查
        print("\n=== 编码问题检查 ===")
        def has_encoding_issues(text):
            if not text:
                return False
            garbled_patterns = ['\u00e4\u00b8', '\u00e5', '\u00e6', '\u00e7']
            return any(pattern in text for pattern in garbled_patterns)
        
        # 检查详情页编码
        if videos:
            detail_result = spider.detailContent([videos[0]['vod_id']])
            if detail_result.get('list'):
                detail = detail_result['list'][0]
                title = detail.get('vod_name', '')
                content = detail.get('vod_content', '')
                
                if not has_encoding_issues(title) and not has_encoding_issues(content):
                    print("✅ 编码检查通过")
                else:
                    print("❌ 仍存在编码问题")
        
        # URL有效性检查
        print("\n=== URL有效性检查 ===")
        valid_pics = 0
        total_pics = 0
        
        for video in videos[:5]:  # 检查前5个视频的图片
            pic = video.get('vod_pic', '')
            if pic:
                total_pics += 1
                if pic.startswith('http'):
                    valid_pics += 1
        
        if total_pics > 0:
            print(f"图片URL有效率: {valid_pics}/{total_pics} ({valid_pics/total_pics*100:.1f}%)")
            if valid_pics/total_pics >= 0.8:
                print("✅ URL有效性检查通过")
            else:
                print("❌ URL有效性检查未通过")
        else:
            print("⚠️ 没有找到图片URL进行检查")
        
        print("\n=== 质量检查完成 ===")
        
    except Exception as e:
        print(f"质量检查过程中出现错误: {e}")

if __name__ == "__main__":
    # 基础功能测试
    test_spider()
    
    # 数据质量检查
    test_data_quality()
