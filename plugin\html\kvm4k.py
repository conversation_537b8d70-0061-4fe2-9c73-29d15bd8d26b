# -*- coding: utf-8 -*-
# 4kvm - https://www.4kvm.net/
# 基于真实网站结构分析的插件实现
import re
import sys
import json
import time
import os
from urllib.parse import quote, unquote
from pyquery import PyQuery as pq

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from base.spider import Spider

class Spider(Spider):
    def init(self, extend=""):
        pass

    def getName(self):
        return "4kvm"

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def destroy(self):
        pass

    host = 'http://www.4kvm.net'  # 使用http协议

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    def homeContent(self, filter):
        """获取首页内容和分类"""
        try:
            response = self.fetch_with_encoding(self.host, headers=self.headers)
            doc = self.getpq(response.text)

            result = {}
            classes = []

            # 基于真实网站结构获取分类导航
            # 网站使用标准的导航菜单结构
            nav_items = doc('header nav ul li a')

            for item in nav_items.items():
                href = item.attr('href') or ''
                text = self.fix_encoding(item.text().strip())

                # 过滤掉首页和外部链接
                if text and text not in ['首页', 'Home', '影片下载'] and href.startswith('/'):
                    # 根据真实URL结构提取分类ID
                    if '/movies' in href:
                        classes.append({
                            'type_name': '电影',
                            'type_id': 'movies'
                        })
                    elif '/tvshows' in href:
                        classes.append({
                            'type_name': '电视剧',
                            'type_id': 'tvshows'
                        })
                    elif '/imdb' in href:
                        classes.append({
                            'type_name': '高分电影',
                            'type_id': 'imdb'
                        })
                    elif '/trending' in href:
                        classes.append({
                            'type_name': '热门播放',
                            'type_id': 'trending'
                        })

            # 获取首页视频列表 - 基于真实网站结构
            videos = []
            # 网站使用article标签包装视频项
            video_items = doc('article')

            if video_items and len(video_items) > 0:
                self.log(f"找到视频容器: article ({len(video_items)}个)")
                seen_ids = set()  # 用于去重

                for item in video_items.items():
                    try:
                        video_data = self.parse_video_item(item, 'home')
                        if video_data and video_data['vod_id'] not in seen_ids:
                            seen_ids.add(video_data['vod_id'])
                            videos.append(video_data)
                    except Exception as e:
                        self.log(f"解析视频项时出错: {e}")
                        continue

            result['class'] = classes
            result['list'] = videos
            self.log(f"首页解析完成: {len(classes)}个分类, {len(videos)}个视频")
            return result

        except Exception as e:
            self.log(f"获取首页内容时出错: {e}")
            return {'class': [], 'list': []}

    def parse_video_item(self, item, page_type="category"):
        """解析视频项数据 - 基于真实网站结构"""
        try:
            # 基于真实网站结构，article内包含链接
            detail_link = item.find('a').eq(0)
            if not detail_link:
                return None

            href = detail_link.attr('href') or ''
            if not href:
                return None

            # 提取视频ID - 基于真实URL结构
            vod_id = ''
            if '/movies/' in href:
                # 电影URL格式: /movies/{slug}
                vod_id = href.split('/movies/')[-1].rstrip('/')
            elif '/tvshows/' in href:
                # 电视剧URL格式: /tvshows/{slug}
                vod_id = href.split('/tvshows/')[-1].rstrip('/')

            if not vod_id:
                return None

            # 获取标题 - 从链接文本或图片alt属性
            title = self.fix_encoding(detail_link.text().strip())
            if not title:
                img_elem = item.find('img')
                if img_elem:
                    title = self.fix_encoding(img_elem.attr('alt') or '')

            if not title:
                return None

            # 获取图片
            pic = ''
            img_elem = item.find('img')
            if img_elem:
                pic = img_elem.attr('src') or ''
                if pic and not pic.startswith('http'):
                    pic = self.host + pic if pic.startswith('/') else ''

            # 获取评分信息作为备注
            remarks = ''
            # 网站在特定div中显示评分
            rating_elem = item.find('div').filter(lambda i, e: 'IMDb' in pq(e).text())
            if rating_elem:
                remarks = self.fix_encoding(rating_elem.text().strip())

            # 获取年份信息
            year = ''
            year_elem = item.find('div').filter(lambda i, e: re.search(r'\d{4}', pq(e).text()))
            if year_elem:
                year_text = year_elem.text()
                year_match = re.search(r'(\d{4})', year_text)
                if year_match:
                    year = year_match.group(1)

            return {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'vod_year': year,
                'vod_remarks': remarks
            }

        except Exception as e:
            self.log(f"解析视频项数据时出错: {e}")
            return None

    def homeVideoContent(self):
        """获取推荐视频"""
        return {'list': []}

    def categoryContent(self, tid, pg, filter, extend):
        """获取分类内容 - 基于真实URL结构"""
        try:
            # 基于真实网站URL结构构建分类URL
            if int(pg) > 1:
                url = f"{self.host}/{tid}/page/{pg}/"
            else:
                url = f"{self.host}/{tid}/"

            response = self.fetch_with_encoding(url, headers=self.headers)
            if response.status_code != 200:
                self.log(f"分类URL访问失败: {url}")
                return {'list': []}

            doc = self.getpq(response.text)

            # 获取视频列表 - 基于真实网站结构
            videos = []
            video_items = doc('article')

            if video_items and len(video_items) > 0:
                seen_ids = set()  # 用于去重

                for item in video_items.items():
                    try:
                        video_data = self.parse_video_item(item, 'category')
                        if video_data and video_data['vod_id'] not in seen_ids:
                            seen_ids.add(video_data['vod_id'])
                            videos.append(video_data)
                    except Exception as e:
                        self.log(f"解析分类视频项时出错: {e}")
                        continue

            self.log(f"分类 {tid} 第 {pg} 页解析完成: {len(videos)}个视频")
            return {'list': videos}

        except Exception as e:
            self.log(f"获取分类内容时出错: {e}")
            return {'list': []}

    def detailContent(self, ids):
        """获取视频详情 - 基于真实网站结构"""
        try:
            vod_id = ids[0]

            # 基于真实网站URL结构构建详情页URL
            # 需要判断是电影还是电视剧
            url_movies = f"{self.host}/movies/{vod_id}"
            url_tvshows = f"{self.host}/tvshows/{vod_id}"

            response = None
            detail_url = ''

            # 先尝试电影URL
            try:
                response = self.fetch_with_encoding(url_movies, headers=self.headers)
                if response.status_code == 200:
                    detail_url = url_movies
                    self.log(f"详情页URL成功(电影): {detail_url}")
            except:
                pass

            # 如果电影URL失败，尝试电视剧URL
            if not response or response.status_code != 200:
                try:
                    response = self.fetch_with_encoding(url_tvshows, headers=self.headers)
                    if response.status_code == 200:
                        detail_url = url_tvshows
                        self.log(f"详情页URL成功(电视剧): {detail_url}")
                except:
                    pass

            if not response or response.status_code != 200:
                self.log("详情页URL访问失败")
                return {'list': []}

            doc = self.getpq(response.text)

            # 获取标题 - 基于真实网站结构
            title = ''
            title_elem = doc('h1')
            if title_elem:
                title = self.fix_encoding(title_elem.text().strip())

            # 获取封面图片
            pic = ''
            img_elem = doc('img').eq(0)  # 第一个图片通常是封面
            if img_elem:
                pic = img_elem.attr('src') or ''
                if pic and not pic.startswith('http'):
                    pic = self.host + pic if pic.startswith('/') else ''

            # 获取简介 - 从段落文本中提取
            content = ''
            content_elem = doc('p')
            if content_elem:
                content = self.fix_encoding(content_elem.text().strip())

            # 获取播放源和播放列表 - 基于真实网站结构
            play_from = []
            play_url = []

            # 查找播放源列表项
            play_items = doc('ul li')
            episodes = []

            for item in play_items.items():
                item_text = item.text().strip()
                # 查找包含播放源信息的项
                if any(keyword in item_text for keyword in ['Watch', 'alists', 'youtube']):
                    source_name = self.fix_encoding(item_text)
                    if source_name:
                        # 简化播放源名称
                        if 'Watch trailer' in source_name:
                            source_name = '预告片'
                        elif 'alists' in source_name:
                            source_name = '在线播放'

                        episodes.append(f"播放${detail_url}")

            if episodes:
                play_from.append('默认播放源')
                play_url.append('#'.join(episodes))

            video_info = {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'vod_actor': '',
                'vod_director': '',
                'vod_content': content,
                'vod_play_from': '$$$'.join(play_from),
                'vod_play_url': '$$$'.join(play_url)
            }

            self.log(f"详情页解析完成: {title}")
            return {'list': [video_info]}

        except Exception as e:
            self.log(f"获取视频详情时出错: {e}")
            return {'list': []}

    def searchContent(self, key, quick, pg):
        """搜索功能 - 基于真实搜索URL"""
        try:
            # 基于真实网站搜索URL结构
            search_url = f"{self.host}/xssearch?s={quote(key)}"

            # 添加分页参数
            if int(pg) > 1:
                search_url = f"{self.host}/xssearch?q={quote(key)}&f=_all&p={pg}"

            response = self.fetch_with_encoding(search_url, headers=self.headers)
            if response.status_code != 200:
                self.log(f"搜索URL访问失败: {search_url}")
                return {'list': []}

            doc = self.getpq(response.text)

            # 获取搜索结果 - 基于真实网站结构
            videos = []
            video_items = doc('article')

            if video_items and len(video_items) > 0:
                seen_ids = set()  # 用于去重

                for item in video_items.items():
                    try:
                        video_data = self.parse_video_item(item, 'search')
                        if video_data and video_data['vod_id'] not in seen_ids:
                            seen_ids.add(video_data['vod_id'])
                            videos.append(video_data)
                    except Exception as e:
                        self.log(f"解析搜索结果项时出错: {e}")
                        continue

            self.log(f"搜索 '{key}' 第 {pg} 页完成: {len(videos)}个结果")
            return {'list': videos}

        except Exception as e:
            self.log(f"搜索时出错: {e}")
            return {'list': []}

    def playerContent(self, flag, id, vipFlags):
        """获取播放地址 - 基于真实网站结构"""
        try:
            # 播放页面URL就是详情页URL
            play_url = id if id.startswith('http') else f"{self.host}{id}"

            response = self.fetch_with_encoding(play_url, headers=self.headers)
            doc = self.getpq(response.text)

            # 查找播放器配置
            video_url = ''

            # 方法1: 从script标签中提取播放器配置
            scripts = doc('script')
            for script in scripts.items():
                script_text = script.text()
                if script_text and ('player' in script_text.lower() or 'video' in script_text.lower()):
                    # 提取可能的视频URL
                    url_patterns = [
                        r'"url"\s*:\s*"([^"]+)"',
                        r"'url'\s*:\s*'([^']+)'",
                        r'src\s*:\s*"([^"]+)"',
                        r"src\s*:\s*'([^']+)'",
                        r'https?://[^"\s]+\.(?:mp4|m3u8|flv|avi|mkv)[^"\s]*'
                    ]

                    for pattern in url_patterns:
                        match = re.search(pattern, script_text)
                        if match:
                            video_url = match.group(1) if match.groups() else match.group(0)
                            break

                    if video_url:
                        break

            # 方法2: 从iframe中提取
            if not video_url:
                iframe_elem = doc('iframe')
                if iframe_elem:
                    iframe_src = iframe_elem.attr('src') or ''
                    if iframe_src:
                        video_url = iframe_src

            # 方法3: 返回详情页让播放器自行处理
            if not video_url:
                video_url = play_url

            self.log(f"播放地址解析: {video_url}")
            return {
                'parse': 1,  # 需要进一步解析
                'playUrl': '',
                'url': video_url
            }

        except Exception as e:
            self.log(f"获取播放地址时出错: {e}")
            return {
                'parse': 0,
                'playUrl': '',
                'url': ''
            }

    def localProxy(self, param):
        pass

    def fix_encoding(self, text):
        """修复UTF-8编码问题"""
        if not text:
            return text

        try:
            # 检查是否包含乱码特征（常见的UTF-8乱码模式）
            garbled_patterns = [
                '\u00e4\u00b8', '\u00e5', '\u00e6', '\u00e7', '\u00e8', '\u00e9',  # 常见乱码前缀
                '\u00c3\u00a4', '\u00c3\u00a5', '\u00c3\u00a6',  # UTF-8被误解为Latin1
                '\u00ef\u00bc', '\u00e2\u0080'  # 标点符号乱码
            ]

            has_garbled = any(pattern in text for pattern in garbled_patterns)

            if has_garbled:
                self.log("检测到编码问题，尝试修复...")

                # 方法1: 尝试Latin1->UTF-8转换
                try:
                    fixed = text.encode('latin1').decode('utf-8')
                    # 检查是否修复成功（包含中文字符）
                    if re.search(r'[\u4e00-\u9fff]', fixed):
                        self.log("使用Latin1->UTF-8修复成功")
                        return fixed
                except Exception as e:
                    self.log(f"Latin1->UTF-8修复失败: {e}")

                # 方法2: 尝试其他编码转换
                encodings = ['cp1252', 'iso-8859-1']
                for encoding in encodings:
                    try:
                        fixed = text.encode(encoding).decode('utf-8')
                        if re.search(r'[\u4e00-\u9fff]', fixed):
                            self.log(f"使用{encoding}->UTF-8修复成功")
                            return fixed
                    except:
                        continue

                self.log("编码修复失败，返回原文本")

            return text

        except Exception as e:
            self.log(f"编码修复异常: {e}")
            return text

    def fetch_with_encoding(self, url, **kwargs):
        """带编码处理的请求方法"""
        try:
            response = self.fetch(url, **kwargs)
            # 确保使用UTF-8编码
            response.encoding = 'utf-8'
            return response
        except Exception as e:
            self.log(f"请求失败: {e}")
            raise

    def getpq(self, text):
        """安全的pyquery解析"""
        try:
            return pq(text)
        except Exception as e:
            self.log(f"pyquery解析出错: {e}")
            try:
                return pq(text.encode('utf-8'))
            except:
                return pq('')
