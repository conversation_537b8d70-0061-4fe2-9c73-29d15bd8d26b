# 4kvm.net 插件使用文档

## 插件概述

**插件名称**: kvm4k  
**目标网站**: http://www.4kvm.net  
**网站类型**: 4K影视网站，提供电影、电视剧、动漫等高清内容  
**插件类型**: html类型爬虫插件  
**开发日期**: 2025年1月  

## 网站特点

- **内容丰富**: 提供电影、电视剧、动漫等多种类型的影视内容
- **高清质量**: 专注于4K高清视频资源
- **分类清晰**: 按类型、年份、评分等多维度分类
- **搜索功能**: 支持关键词搜索
- **评分系统**: 集成IMDb评分信息

## 插件功能

### 1. 首页内容获取 (homeContent)
- **功能**: 获取网站首页的分类导航和推荐视频
- **返回数据**:
  - 分类列表: 电影、电视剧、高分电影、热门播放
  - 首页推荐视频列表

### 2. 分类内容获取 (categoryContent)
- **功能**: 获取指定分类下的视频列表
- **支持分类**:
  - `movies`: 电影
  - `tvshows`: 电视剧
  - `imdb`: 高分电影
  - `trending`: 热门播放
- **支持分页**: 是

### 3. 搜索功能 (searchContent)
- **功能**: 根据关键词搜索视频
- **搜索范围**: 全站视频内容
- **支持分页**: 是

### 4. 详情页获取 (detailContent)
- **功能**: 获取视频详细信息
- **返回信息**:
  - 视频标题、封面图片
  - 视频简介
  - 播放源和播放链接

### 5. 播放地址解析 (playerContent)
- **功能**: 解析视频播放地址
- **支持格式**: 多种视频格式和播放源

## 技术实现

### URL结构分析
```
首页: http://www.4kvm.net/
电影分类: http://www.4kvm.net/movies/
电视剧分类: http://www.4kvm.net/tvshows/
详情页: http://www.4kvm.net/movies/{slug} 或 /tvshows/{slug}
搜索: http://www.4kvm.net/xssearch?s={关键词}
分页: http://www.4kvm.net/{category}/page/{页码}/
```

### HTML结构特点
- **视频容器**: `<article>` 标签
- **视频链接**: `<a href="/movies/{slug}">` 或 `<a href="/tvshows/{slug}">`
- **图片元素**: `<img>` 标签，可能包含懒加载
- **评分信息**: 包含IMDb评分的特定div元素

### 数据质量保证
1. **编码修复**: 自动检测和修复UTF-8编码问题
2. **数据去重**: 使用ID集合确保数据唯一性
3. **错误处理**: 完善的异常捕获和日志记录
4. **智能选择器**: 多种选择器策略确保数据提取成功

## 使用方法

### 1. 安装依赖
```bash
pip install pyquery requests lxml
```

### 2. 插件配置
将 `kvm4k.py` 文件放置在 `plugin/html/` 目录下

### 3. 基本使用
```python
from kvm4k import Spider

# 创建插件实例
spider = Spider()
spider.init()

# 获取首页内容
home_result = spider.homeContent({})
print(f"分类数量: {len(home_result['class'])}")
print(f"视频数量: {len(home_result['list'])}")

# 获取分类内容
category_result = spider.categoryContent('movies', '1', {}, {})
print(f"电影数量: {len(category_result['list'])}")

# 搜索功能
search_result = spider.searchContent('哪吒', False, '1')
print(f"搜索结果: {len(search_result['list'])}")

# 获取详情
if search_result['list']:
    video_id = search_result['list'][0]['vod_id']
    detail_result = spider.detailContent([video_id])
    print(f"详情: {detail_result['list'][0]['vod_name']}")
```

## 配置参数

### Headers配置
```python
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
}
```

### 超时设置
- 默认请求超时: 30秒
- 重试次数: 3次
- 重试间隔: 1秒

## 常见问题

### 1. 网络连接问题
**问题**: 出现连接超时或连接被拒绝  
**解决方案**: 
- 检查网络连接
- 尝试使用代理
- 调整请求头信息

### 2. 数据解析失败
**问题**: 返回空数据或解析错误  
**解决方案**:
- 检查网站结构是否发生变化
- 查看日志输出定位问题
- 更新选择器策略

### 3. 编码问题
**问题**: 中文显示乱码  
**解决方案**: 插件已内置编码修复功能，会自动处理

## 维护说明

### 更新频率
建议每月检查一次网站结构变化，及时更新插件

### 监控指标
- 数据获取成功率
- 响应时间
- 数据质量（去重率、完整性）

### 日志记录
插件会记录以下信息：
- 请求URL和响应状态
- 数据解析结果
- 错误信息和异常堆栈

## 版本历史

### v1.0 (2025-01-29)
- 初始版本发布
- 实现基本的爬虫功能
- 支持首页、分类、搜索、详情、播放地址解析
- 集成数据质量保证机制

## 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. 依赖包是否正确安装
3. 网站是否可正常访问
4. 插件日志输出信息

## 免责声明

本插件仅用于技术学习和研究目的，请遵守相关法律法规和网站使用条款。
