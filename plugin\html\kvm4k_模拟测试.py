# -*- coding: utf-8 -*-
# kvm4k插件模拟测试脚本
import sys
import os

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

def test_parse_video_item():
    """测试视频项解析功能"""
    print("=" * 50)
    print("测试视频项解析功能")
    print("=" * 50)
    
    try:
        from kvm4k import Spider
        from pyquery import PyQuery as pq
        
        spider = Spider()
        spider.init()
        
        # 模拟HTML数据
        test_html_cases = [
            # 案例1: 标准的视频项
            '''
            <div class="module-item">
                <a href="/detail/12345/" title="测试电影">
                    <img src="/images/poster1.jpg" alt="测试电影" data-original="/images/poster1_hd.jpg">
                    <div class="module-item-note">HD</div>
                </a>
            </div>
            ''',
            
            # 案例2: 链接在内部的结构
            '''
            <div class="video-item">
                <div class="pic">
                    <a href="/video/67890/">
                        <img data-src="/images/poster2.jpg" alt="测试剧集">
                    </a>
                </div>
                <div class="note">更新至10集</div>
            </div>
            ''',
            
            # 案例3: 播放链接格式
            '''
            <div class="movie-item">
                <a href="/play/11111-1-1/" title="测试动漫">
                    <img src="placeholder.jpg" data-original="/images/poster3.jpg">
                </a>
                <span class="year">2024</span>
            </div>
            '''
        ]
        
        for i, html in enumerate(test_html_cases, 1):
            print(f"\n--- 测试案例 {i} ---")
            doc = pq(html)
            item = doc('.module-item, .video-item, .movie-item').eq(0)
            
            if item:
                result = spider.parse_video_item(item, 'test')
                if result:
                    print(f"✅ 解析成功:")
                    print(f"   ID: {result['vod_id']}")
                    print(f"   标题: {result['vod_name']}")
                    print(f"   图片: {result['vod_pic']}")
                    print(f"   备注: {result['vod_remarks']}")
                else:
                    print("❌ 解析失败")
            else:
                print("❌ 未找到视频项")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_encoding_fix():
    """测试编码修复功能"""
    print("\n" + "=" * 50)
    print("测试编码修复功能")
    print("=" * 50)
    
    try:
        from kvm4k import Spider
        
        spider = Spider()
        
        # 测试用例
        test_cases = [
            ("正常中文", "正常中文"),
            ("", ""),
            (None, None),
            ("English text", "English text"),
            # 模拟乱码情况
            ("测试\u00e4\u00b8\u00ad\u00e6\u0096\u0087", "测试\u00e4\u00b8\u00ad\u00e6\u0096\u0087"),  # 这个会触发修复逻辑
        ]
        
        for input_text, expected in test_cases:
            result = spider.fix_encoding(input_text)
            status = "✅" if result == expected or (input_text and result) else "❌"
            print(f"{status} 输入: {repr(input_text)} -> 输出: {repr(result)}")
        
    except Exception as e:
        print(f"❌ 编码测试失败: {e}")

def test_url_extraction():
    """测试URL和ID提取功能"""
    print("\n" + "=" * 50)
    print("测试URL和ID提取功能")
    print("=" * 50)
    
    try:
        from kvm4k import Spider
        from pyquery import PyQuery as pq
        
        spider = Spider()
        
        # 测试URL模式
        test_urls = [
            ("/detail/12345/", "12345"),
            ("/video/67890/", "67890"),
            ("/play/11111-1-1/", "11111"),
            ("/movie/22222/", "22222"),
            ("https://www.4kvm.net/detail/33333/", "33333"),
            ("/some/path/44444/", "44444"),
        ]
        
        print("URL ID提取测试:")
        for url, expected_id in test_urls:
            # 模拟parse_video_item中的ID提取逻辑
            vod_id = ''
            if '/detail/' in url:
                vod_id = url.split('/detail/')[-1].rstrip('/')
            elif '/video/' in url:
                vod_id = url.split('/video/')[-1].rstrip('/')
            elif '/play/' in url:
                vod_id = url.split('/play/')[-1].split('-')[0]
            else:
                import re
                match = re.search(r'/(\d+)/?$', url)
                if match:
                    vod_id = match.group(1)
            
            status = "✅" if vod_id == expected_id else "❌"
            print(f"{status} {url} -> ID: {vod_id} (期望: {expected_id})")
        
    except Exception as e:
        print(f"❌ URL提取测试失败: {e}")

def test_data_deduplication():
    """测试数据去重功能"""
    print("\n" + "=" * 50)
    print("测试数据去重功能")
    print("=" * 50)
    
    try:
        # 模拟重复数据
        test_videos = [
            {'vod_id': '001', 'vod_name': '电影1'},
            {'vod_id': '002', 'vod_name': '电影2'},
            {'vod_id': '001', 'vod_name': '电影1重复'},  # 重复ID
            {'vod_id': '003', 'vod_name': '电影3'},
            {'vod_id': '002', 'vod_name': '电影2重复'},  # 重复ID
        ]
        
        # 模拟去重逻辑
        seen_ids = set()
        unique_videos = []
        
        for video in test_videos:
            if video['vod_id'] not in seen_ids:
                seen_ids.add(video['vod_id'])
                unique_videos.append(video)
        
        print(f"原始数据: {len(test_videos)} 个")
        print(f"去重后: {len(unique_videos)} 个")
        print(f"重复数量: {len(test_videos) - len(unique_videos)}")
        
        if len(unique_videos) == 3:  # 期望结果
            print("✅ 去重功能正常")
        else:
            print("❌ 去重功能异常")
        
        print("\n去重后的数据:")
        for video in unique_videos:
            print(f"  {video['vod_id']}: {video['vod_name']}")
        
    except Exception as e:
        print(f"❌ 去重测试失败: {e}")

def test_selector_strategies():
    """测试选择器策略"""
    print("\n" + "=" * 50)
    print("测试选择器策略")
    print("=" * 50)
    
    try:
        from pyquery import PyQuery as pq
        
        # 模拟不同网站结构的HTML
        test_htmls = [
            # 结构1: 使用 .module-item
            '''
            <div class="content">
                <div class="module-item"><a href="/detail/1/">电影1</a></div>
                <div class="module-item"><a href="/detail/2/">电影2</a></div>
            </div>
            ''',
            
            # 结构2: 使用 .video-item
            '''
            <div class="content">
                <div class="video-item"><a href="/video/3/">剧集1</a></div>
                <div class="video-item"><a href="/video/4/">剧集2</a></div>
            </div>
            ''',
            
            # 结构3: 直接使用链接
            '''
            <div class="content">
                <a href="/detail/5/">动漫1</a>
                <a href="/detail/6/">动漫2</a>
            </div>
            '''
        ]
        
        selectors = ['.module-item', '.video-item', '.movie-item', 'a[href*="/detail/"]']
        
        for i, html in enumerate(test_htmls, 1):
            print(f"\n--- HTML结构 {i} ---")
            doc = pq(html)
            
            found = False
            for selector in selectors:
                items = doc(selector)
                if items and len(items) > 0:
                    print(f"✅ 选择器 '{selector}' 找到 {len(items)} 个项目")
                    found = True
                    break
            
            if not found:
                print("❌ 没有找到合适的选择器")
        
    except Exception as e:
        print(f"❌ 选择器测试失败: {e}")

if __name__ == "__main__":
    print("开始kvm4k插件模拟测试")
    print("由于无法访问真实网站，使用模拟数据进行测试")
    
    # 运行所有测试
    test_parse_video_item()
    test_encoding_fix()
    test_url_extraction()
    test_data_deduplication()
    test_selector_strategies()
    
    print("\n" + "=" * 50)
    print("模拟测试完成")
    print("=" * 50)
