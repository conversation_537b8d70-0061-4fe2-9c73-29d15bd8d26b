# -*- coding: utf-8 -*-
# kvm4k插件真实网站测试脚本
import sys
import os

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

def test_real_website():
    """测试真实网站功能"""
    print("=" * 60)
    print("4kvm插件真实网站测试")
    print("=" * 60)
    
    try:
        from kvm4k import Spider
        
        spider = Spider()
        spider.init()
        
        print(f"网站地址: {spider.host}")
        print(f"插件名称: {spider.getName()}")
        
        # 测试首页内容
        print("\n=== 测试首页内容 ===")
        try:
            home_result = spider.homeContent({})
            classes = home_result.get('class', [])
            videos = home_result.get('list', [])
            
            print(f"✅ 分类数量: {len(classes)}")
            if classes:
                print("分类列表:")
                for cls in classes:
                    print(f"  - {cls['type_name']} (ID: {cls['type_id']})")
            
            print(f"✅ 首页视频数量: {len(videos)}")
            if videos:
                print("前3个视频:")
                for i, video in enumerate(videos[:3]):
                    print(f"  {i+1}. {video['vod_name']}")
                    print(f"     ID: {video['vod_id']}")
                    print(f"     年份: {video['vod_year']}")
                    print(f"     备注: {video['vod_remarks']}")
                    print(f"     图片: {video['vod_pic'][:50]}..." if video['vod_pic'] else "     图片: 无")
                    print()
        except Exception as e:
            print(f"❌ 首页测试失败: {e}")
        
        # 测试分类内容
        print("\n=== 测试分类内容 ===")
        try:
            if classes:
                first_category = classes[0]
                print(f"测试分类: {first_category['type_name']}")
                category_result = spider.categoryContent(first_category['type_id'], '1', {}, {})
                category_videos = category_result.get('list', [])
                
                print(f"✅ 分类视频数量: {len(category_videos)}")
                if category_videos:
                    print("前2个视频:")
                    for i, video in enumerate(category_videos[:2]):
                        print(f"  {i+1}. {video['vod_name']} (ID: {video['vod_id']})")
        except Exception as e:
            print(f"❌ 分类测试失败: {e}")
        
        # 测试搜索功能
        print("\n=== 测试搜索功能 ===")
        try:
            search_result = spider.searchContent('哪吒', False, '1')
            search_videos = search_result.get('list', [])
            
            print(f"✅ 搜索 '哪吒' 结果数量: {len(search_videos)}")
            if search_videos:
                print("搜索结果:")
                for i, video in enumerate(search_videos[:3]):
                    print(f"  {i+1}. {video['vod_name']} (ID: {video['vod_id']})")
        except Exception as e:
            print(f"❌ 搜索测试失败: {e}")
        
        # 测试详情页面
        print("\n=== 测试详情页面 ===")
        try:
            # 使用已知的视频ID进行测试
            test_id = 'nazhnaohai'  # 哪吒之魔童闹海
            detail_result = spider.detailContent([test_id])
            
            if detail_result.get('list'):
                detail = detail_result['list'][0]
                print(f"✅ 详情页解析成功:")
                print(f"  标题: {detail.get('vod_name', '')}")
                print(f"  简介: {detail.get('vod_content', '')[:100]}..." if detail.get('vod_content') else "  简介: 无")
                
                play_from = detail.get('vod_play_from', '')
                play_url = detail.get('vod_play_url', '')
                
                if play_from:
                    sources = play_from.split('$$$')
                    print(f"  播放源数量: {len(sources)}")
                    print(f"  播放源: {', '.join(sources)}")
                
                if play_url:
                    episodes = play_url.split('$$$')[0].split('#') if play_url else []
                    print(f"  播放链接数量: {len(episodes)}")
            else:
                print("❌ 详情页面解析失败")
        except Exception as e:
            print(f"❌ 详情测试失败: {e}")
        
        # 测试播放地址解析
        print("\n=== 测试播放地址解析 ===")
        try:
            if detail_result.get('list') and detail_result['list'][0].get('vod_play_url'):
                play_urls = detail_result['list'][0]['vod_play_url'].split('$$$')
                if play_urls and play_urls[0]:
                    first_episode = play_urls[0].split('#')[0]
                    if '$' in first_episode:
                        episode_url = first_episode.split('$')[1]
                        
                        player_result = spider.playerContent('默认播放源', episode_url, [])
                        print(f"✅ 播放地址解析:")
                        print(f"  解析模式: {player_result.get('parse', 0)}")
                        print(f"  播放地址: {player_result.get('url', '')[:100]}..." if player_result.get('url') else "  播放地址: 无")
        except Exception as e:
            print(f"❌ 播放地址测试失败: {e}")
        
        print("\n" + "=" * 60)
        print("真实网站测试完成")
        print("=" * 60)
        
    except ImportError as e:
        print(f"❌ 导入插件失败: {e}")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def test_data_quality():
    """数据质量检查"""
    print("\n" + "=" * 60)
    print("数据质量检查")
    print("=" * 60)
    
    try:
        from kvm4k import Spider
        
        spider = Spider()
        spider.init()
        
        # 测试编码修复
        print("=== 编码修复测试 ===")
        test_cases = [
            "正常中文文本",
            "English text",
            "",
            None
        ]
        
        for text in test_cases:
            result = spider.fix_encoding(text)
            print(f"输入: {repr(text)} -> 输出: {repr(result)}")
        
        # 测试去重功能
        print("\n=== 去重功能测试 ===")
        home_result = spider.homeContent({})
        videos = home_result.get('list', [])
        
        if videos:
            video_ids = [v['vod_id'] for v in videos]
            unique_ids = set(video_ids)
            
            print(f"视频总数: {len(videos)}")
            print(f"唯一ID数: {len(unique_ids)}")
            print(f"重复数量: {len(video_ids) - len(unique_ids)}")
            
            if len(video_ids) == len(unique_ids):
                print("✅ 去重检查通过")
            else:
                print("❌ 存在重复视频")
        
        # 测试数据完整性
        print("\n=== 数据完整性测试 ===")
        valid_count = 0
        total_count = len(videos)
        
        for video in videos:
            if video.get('vod_id') and video.get('vod_name'):
                valid_count += 1
        
        print(f"有效视频: {valid_count}/{total_count}")
        if valid_count == total_count:
            print("✅ 数据完整性检查通过")
        else:
            print("❌ 存在无效数据")
        
        print("\n" + "=" * 60)
        print("数据质量检查完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 质量检查失败: {e}")

if __name__ == "__main__":
    # 运行真实网站测试
    test_real_website()
    
    # 运行数据质量检查
    test_data_quality()
