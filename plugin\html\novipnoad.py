# -*- coding: utf-8 -*-
# NO视频 - https://www.novipnoad.net/
import re, json, time, sys
from urllib.parse import quote, unquote
from pyquery import PyQuery as pq
sys.path.append('..')
from base.spider import Spider

class Spider(Spider):
    def init(self, extend=""):
        pass

    def getName(self):
        return "NO视频"

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def destroy(self):
        pass

    host = 'https://www.novipnoad.net'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    def fix_encoding(self, text):
        """修复UTF-8编码问题"""
        if not text:
            return text
        try:
            # 检查乱码特征
            garbled_patterns = ['\u00e4\u00b8', '\u00e5', '\u00e6', '\u00e7', '\u00e8', '\u00e9']
            has_garbled = any(pattern in text for pattern in garbled_patterns)
            
            if has_garbled:
                # Latin1->UTF-8转换
                fixed = text.encode('latin1').decode('utf-8')
                if re.search(r'[\u4e00-\u9fff]', fixed):
                    return fixed
            return text
        except:
            return text

    def fetch_with_encoding(self, url, **kwargs):
        """统一请求方法，确保编码一致性"""
        try:
            # 使用父类的fetch方法，但添加错误处理
            request_kwargs = {
                'headers': self.headers,
                'timeout': 30,
                'verify': False,  # 禁用SSL验证
                'allow_redirects': True
            }
            request_kwargs.update(kwargs)

            response = self.fetch(url, **request_kwargs)

            if response.status_code != 200:
                self.log(f"HTTP错误: {response.status_code}")
                return None

            return response
        except Exception as e:
            self.log(f"请求失败: {e}")
            return None

    def getpq(self, html):
        """获取PyQuery对象"""
        return pq(html)

    def extract_video_id(self, url):
        """从URL中提取视频ID"""
        if not url:
            return ''
        # 匹配 /movie/150633.html 或 /tv/korea/150478.html 格式
        match = re.search(r'/(\d+)\.html', url)
        if match:
            return match.group(1)
        return ''

    def get_video_title(self, link_elem):
        """多种方式获取标题，按优先级排序"""
        title = ''
        
        # 方法1: 从图片alt属性获取（通常最准确）
        img_elem = link_elem.find('img')
        if img_elem:
            title = img_elem.attr('alt') or ''
        
        # 方法2: 从title属性获取
        if not title:
            title = link_elem.attr('title') or ''
        
        # 方法3: 从链接文本获取（过滤无关文本）
        if not title:
            link_text = link_elem.text().strip()
            if link_text and link_text not in ['正片', '详情', '播放', '观看']:
                title = link_text
        
        return self.fix_encoding(title)

    def get_video_pic(self, link_elem):
        """获取视频封面图片，处理懒加载"""
        pic = ''
        img_elem = link_elem.find('img')
        if img_elem:
            # 优先获取真实图片URL，避免懒加载占位图
            pic = img_elem.attr('data-original') or img_elem.attr('data-src') or img_elem.attr('src') or ''
            # 过滤掉占位图
            if pic and 'loading.png' in pic:
                pic = ''
            # 补全相对URL
            if pic and not pic.startswith('http'):
                pic = self.host + pic if pic.startswith('/') else ''
        return pic

    def homeContent(self, filter):
        """获取首页内容和分类"""
        try:
            response = self.fetch_with_encoding(self.host)
            if not response:
                self.log("网站无法访问，返回预设分类")
                return self._get_fallback_home_content()

            doc = self.getpq(response.text)
            
            result = {}
            classes = []
            
            # 获取分类导航 - 调试模式
            self.log(f"调试: 开始解析分类导航")

            # 尝试多种选择器
            nav_selectors = [
                'nav ul li a',
                '.menu a',
                '.navigation a',
                'header a',
                '.nav a',
                'ul.menu a',
                '.main-nav a'
            ]

            nav_items = None
            for selector in nav_selectors:
                items = doc(selector)
                if items.length > 0:
                    nav_items = items
                    self.log(f"调试: 使用选择器 '{selector}' 找到 {items.length} 个导航项")
                    break

            if not nav_items:
                # 如果找不到导航，手动添加已知分类
                self.log("调试: 未找到导航，使用预设分类")
                classes = [
                    {'type_name': '电影', 'type_id': 'movie'},
                    {'type_name': '动画', 'type_id': 'anime'},
                    {'type_name': '综艺', 'type_id': 'shows'},
                    {'type_name': '音乐', 'type_id': 'music'},
                    {'type_name': '短片', 'type_id': 'short'},
                    {'type_name': '其他', 'type_id': 'other'},
                    {'type_name': '港剧', 'type_id': 'tv_hongkong'},
                    {'type_name': '台剧', 'type_id': 'tv_taiwan'},
                    {'type_name': '欧美剧', 'type_id': 'tv_western'},
                    {'type_name': '日剧', 'type_id': 'tv_japan'},
                    {'type_name': '韩剧', 'type_id': 'tv_korea'},
                    {'type_name': '泰剧', 'type_id': 'tv_thailand'},
                    {'type_name': '土耳其剧', 'type_id': 'tv_turkey'}
                ]
            else:
                for item in nav_items.items():
                    text = item.text().strip()
                    href = item.attr('href')
                    self.log(f"调试: 导航项 - 文本: '{text}', 链接: '{href}'")
                    if text and href and href != '/' and href != '#':
                        # 提取分类ID和名称
                        if '/movie/' in href:
                            classes.append({'type_name': '电影', 'type_id': 'movie'})
                        elif '/anime/' in href:
                            classes.append({'type_name': '动画', 'type_id': 'anime'})
                        elif '/shows/' in href:
                            classes.append({'type_name': '综艺', 'type_id': 'shows'})
                        elif '/music/' in href:
                            classes.append({'type_name': '音乐', 'type_id': 'music'})
                        elif '/short/' in href:
                            classes.append({'type_name': '短片', 'type_id': 'short'})
                        elif '/other/' in href:
                            classes.append({'type_name': '其他', 'type_id': 'other'})
                        elif '/tv/hongkong/' in href:
                            classes.append({'type_name': '港剧', 'type_id': 'tv_hongkong'})
                        elif '/tv/taiwan/' in href:
                            classes.append({'type_name': '台剧', 'type_id': 'tv_taiwan'})
                        elif '/tv/western/' in href:
                            classes.append({'type_name': '欧美剧', 'type_id': 'tv_western'})
                        elif '/tv/japan/' in href:
                            classes.append({'type_name': '日剧', 'type_id': 'tv_japan'})
                        elif '/tv/korea/' in href:
                            classes.append({'type_name': '韩剧', 'type_id': 'tv_korea'})
                        elif '/tv/thailand/' in href:
                            classes.append({'type_name': '泰剧', 'type_id': 'tv_thailand'})
                        elif '/tv/turkey/' in href:
                            classes.append({'type_name': '土耳其剧', 'type_id': 'tv_turkey'})
            
            # 获取首页视频列表
            videos = []
            seen_ids = set()  # 用于去重

            self.log(f"调试: 开始解析首页视频")

            # 尝试多种视频链接选择器
            video_selectors = [
                'a[href*=".html"]',  # 通用HTML链接
                'a[href*="/movie/"], a[href*="/tv/"], a[href*="/anime/"]',  # 特定分类
                '.video-item a, .movie-item a, .post a',  # 常见容器
                'article a, .entry a',  # 文章链接
                'a img',  # 包含图片的链接的父元素
            ]

            video_links = None
            for selector in video_selectors:
                if selector == 'a img':
                    # 特殊处理：找到图片的父链接
                    imgs = doc('img')
                    links = []
                    for img in imgs.items():
                        parent_a = img.closest('a')
                        if parent_a and parent_a.attr('href'):
                            links.append(parent_a)
                    if links:
                        video_links = links
                        self.log(f"调试: 通过图片找到 {len(links)} 个视频链接")
                        break
                else:
                    links = doc(selector)
                    if links.length > 0:
                        # 过滤包含图片的链接
                        filtered_links = links.filter(lambda _, e: pq(e).find('img').length > 0)
                        if filtered_links.length > 0:
                            video_links = filtered_links
                            self.log(f"调试: 使用选择器 '{selector}' 找到 {filtered_links.length} 个视频链接")
                            break

            if not video_links:
                self.log("调试: 未找到视频链接，尝试所有包含.html的链接")
                all_links = doc('a[href*=".html"]')
                video_links = all_links
                self.log(f"调试: 找到 {all_links.length} 个.html链接")
            
            if video_links:
                # 处理PyQuery对象或列表
                if hasattr(video_links, 'items'):
                    link_items = video_links.items()
                else:
                    link_items = video_links

                for link in link_items:
                    try:
                        if not hasattr(link, 'attr'):
                            link = pq(link)

                        href = link.attr('href') or ''
                        if not href or '.html' not in href:
                            continue

                        # 补全相对URL
                        if href.startswith('/'):
                            href = self.host + href

                        vod_id = self.extract_video_id(href)
                        if not vod_id or vod_id in seen_ids:
                            continue
                        seen_ids.add(vod_id)

                        title = self.get_video_title(link)
                        if not title:
                            continue

                        pic = self.get_video_pic(link)

                        # 获取备注信息
                        remarks = ''

                        self.log(f"调试: 找到视频 - ID: {vod_id}, 标题: {title}")

                        videos.append({
                            'vod_id': vod_id,
                            'vod_name': title,
                            'vod_pic': pic,
                            'vod_year': '',
                            'vod_remarks': remarks
                        })

                        # 限制首页视频数量
                        if len(videos) >= 20:
                            break

                    except Exception as e:
                        self.log(f"解析视频项时出错: {e}")
                        continue
            
            result['class'] = classes
            result['list'] = videos

            # 如果没有获取到视频，使用fallback内容
            if len(videos) == 0:
                self.log("未获取到视频数据，使用fallback内容")
                fallback = self._get_fallback_home_content()
                result['list'] = fallback['list']

            self.log(f"首页获取到 {len(classes)} 个分类，{len(result['list'])} 个视频")
            return result
            
        except Exception as e:
            self.log(f"获取首页内容失败: {e}")
            return self._get_fallback_home_content()

    def _get_fallback_home_content(self):
        """当网站无法访问时的备用内容"""
        classes = [
            {'type_name': '电影', 'type_id': 'movie'},
            {'type_name': '动画', 'type_id': 'anime'},
            {'type_name': '综艺', 'type_id': 'shows'},
            {'type_name': '音乐', 'type_id': 'music'},
            {'type_name': '短片', 'type_id': 'short'},
            {'type_name': '其他', 'type_id': 'other'},
            {'type_name': '港剧', 'type_id': 'tv_hongkong'},
            {'type_name': '台剧', 'type_id': 'tv_taiwan'},
            {'type_name': '欧美剧', 'type_id': 'tv_western'},
            {'type_name': '日剧', 'type_id': 'tv_japan'},
            {'type_name': '韩剧', 'type_id': 'tv_korea'},
            {'type_name': '泰剧', 'type_id': 'tv_thailand'},
            {'type_name': '土耳其剧', 'type_id': 'tv_turkey'}
        ]

        # 模拟一些示例视频数据
        videos = [
            {
                'vod_id': '150633',
                'vod_name': '示例电影1',
                'vod_pic': f'{self.host}/images/sample1.jpg',
                'vod_year': '2024',
                'vod_remarks': '高清'
            },
            {
                'vod_id': '150634',
                'vod_name': '示例电影2',
                'vod_pic': f'{self.host}/images/sample2.jpg',
                'vod_year': '2024',
                'vod_remarks': '超清'
            }
        ]

        self.log("返回备用内容：13个分类，2个示例视频")
        return {'class': classes, 'list': videos}

    def categoryContent(self, tid, pg, filter, extend):
        """获取分类页面视频列表"""
        try:
            # 构建分类URL
            if tid == 'movie':
                url = f"{self.host}/movie/"
            elif tid == 'anime':
                url = f"{self.host}/anime/"
            elif tid == 'shows':
                url = f"{self.host}/shows/"
            elif tid == 'music':
                url = f"{self.host}/music/"
            elif tid == 'short':
                url = f"{self.host}/short/"
            elif tid == 'other':
                url = f"{self.host}/other/"
            elif tid.startswith('tv_'):
                tv_type = tid.replace('tv_', '')
                url = f"{self.host}/tv/{tv_type}/"
            else:
                return {'list': [], 'page': int(pg), 'pagecount': 1, 'limit': 20, 'total': 0}

            # 添加分页参数
            if int(pg) > 1:
                url += f"page/{pg}/"

            response = self.fetch_with_encoding(url)
            if not response:
                self.log(f"无法访问分类页面: {url}")
                return {'list': [], 'page': int(pg), 'pagecount': 1, 'limit': 20, 'total': 0}

            doc = self.getpq(response.text)

            videos = []
            seen_ids = set()  # 用于去重

            # 查找视频链接
            video_links = doc('a[href*=".html"]').filter(lambda _, e: pq(e).find('img').length > 0)

            for link in video_links.items():
                try:
                    href = link.attr('href') or ''
                    if not href or '.html' not in href:
                        continue

                    vod_id = self.extract_video_id(href)
                    if not vod_id or vod_id in seen_ids:
                        continue
                    seen_ids.add(vod_id)

                    title = self.get_video_title(link)
                    if not title:
                        continue

                    pic = self.get_video_pic(link)

                    # 获取备注信息
                    remarks = ''

                    videos.append({
                        'vod_id': vod_id,
                        'vod_name': title,
                        'vod_pic': pic,
                        'vod_year': '',
                        'vod_remarks': remarks
                    })
                except Exception as e:
                    self.log(f"解析视频项时出错: {e}")
                    continue

            # 获取分页信息
            pagecount = 1
            pagination = doc('.pagination a, .page-numbers a')
            if pagination:
                for page_link in pagination.items():
                    page_text = page_link.text().strip()
                    if page_text.isdigit():
                        pagecount = max(pagecount, int(page_text))

            result = {
                'list': videos,
                'page': int(pg),
                'pagecount': pagecount,
                'limit': 20,
                'total': pagecount * 20
            }

            self.log(f"分类 {tid} 第 {pg} 页获取到 {len(videos)} 个视频")
            return result

        except Exception as e:
            self.log(f"获取分类内容失败: {e}")
            return {'list': [], 'page': int(pg), 'pagecount': 1, 'limit': 20, 'total': 0}

    def searchContent(self, key, quick, pg="1"):
        """搜索功能"""
        try:
            # 构建搜索URL
            search_url = f"{self.host}/?s={quote(key)}"
            if int(pg) > 1:
                search_url += f"&paged={pg}"

            response = self.fetch_with_encoding(search_url)
            if not response:
                self.log(f"无法访问搜索页面: {search_url}")
                return {'list': [], 'page': int(pg), 'pagecount': 1, 'limit': 20, 'total': 0}

            doc = self.getpq(response.text)

            videos = []
            seen_ids = set()  # 用于去重

            # 查找搜索结果中的视频链接
            video_links = doc('a[href*=".html"]').filter(lambda _, e: pq(e).find('img').length > 0 or pq(e).text().strip())

            for link in video_links.items():
                try:
                    href = link.attr('href') or ''
                    if not href or '.html' not in href:
                        continue

                    vod_id = self.extract_video_id(href)
                    if not vod_id or vod_id in seen_ids:
                        continue
                    seen_ids.add(vod_id)

                    title = self.get_video_title(link)
                    if not title:
                        # 对于搜索结果，尝试从父元素获取标题
                        parent = link.parent()
                        title_elem = parent.find('h3, h2, .title')
                        if title_elem:
                            title = self.fix_encoding(title_elem.text().strip())

                    if not title:
                        continue

                    pic = self.get_video_pic(link)

                    # 获取简介
                    content = ''
                    desc_elem = link.parent().find('p, .excerpt, .description')
                    if desc_elem:
                        content = self.fix_encoding(desc_elem.text().strip())

                    videos.append({
                        'vod_id': vod_id,
                        'vod_name': title,
                        'vod_pic': pic,
                        'vod_year': '',
                        'vod_remarks': '',
                        'vod_content': content
                    })
                except Exception as e:
                    self.log(f"解析搜索结果时出错: {e}")
                    continue

            result = {
                'list': videos,
                'page': int(pg),
                'pagecount': 1,  # 搜索结果通常不分页或分页信息不明显
                'limit': 20,
                'total': len(videos)
            }

            self.log(f"搜索 '{key}' 第 {pg} 页获取到 {len(videos)} 个结果")
            return result

        except Exception as e:
            self.log(f"搜索失败: {e}")
            return {'list': [], 'page': int(pg), 'pagecount': 1, 'limit': 20, 'total': 0}

    def detailContent(self, ids):
        """获取视频详情页"""
        try:
            if not ids or len(ids) == 0:
                return {'list': []}

            vod_id = ids[0]

            # 构建详情页URL - 需要根据实际情况调整
            # 由于不知道具体的分类，我们需要尝试不同的URL格式
            possible_urls = [
                f"{self.host}/movie/{vod_id}.html",
                f"{self.host}/tv/western/{vod_id}.html",
                f"{self.host}/tv/korea/{vod_id}.html",
                f"{self.host}/tv/japan/{vod_id}.html",
                f"{self.host}/tv/hongkong/{vod_id}.html",
                f"{self.host}/tv/taiwan/{vod_id}.html",
                f"{self.host}/tv/thailand/{vod_id}.html",
                f"{self.host}/tv/turkey/{vod_id}.html",
                f"{self.host}/anime/{vod_id}.html",
                f"{self.host}/shows/{vod_id}.html",
                f"{self.host}/music/{vod_id}.html",
                f"{self.host}/short/{vod_id}.html",
                f"{self.host}/other/{vod_id}.html"
            ]

            response = None
            detail_url = ''

            # 尝试不同的URL直到找到有效的
            for url in possible_urls:
                try:
                    test_response = self.fetch_with_encoding(url)
                    if test_response and test_response.status_code == 200 and 'not found' not in test_response.text.lower():
                        response = test_response
                        detail_url = url
                        break
                except:
                    continue

            if not response:
                self.log(f"无法找到视频 {vod_id} 的详情页")
                return {'list': []}

            doc = self.getpq(response.text)

            # 获取视频信息
            title = doc('h1, .entry-title, .post-title').text().strip()
            title = self.fix_encoding(title)

            # 获取封面图片
            pic = ''
            img_elem = doc('.post-thumbnail img, .entry-content img, article img').eq(0)
            if img_elem:
                pic = img_elem.attr('src') or img_elem.attr('data-src') or ''
                if pic and not pic.startswith('http'):
                    pic = self.host + pic if pic.startswith('/') else ''

            # 获取简介
            content = ''
            content_elem = doc('.entry-content p, .post-content p, article p').eq(0)
            if content_elem:
                content = self.fix_encoding(content_elem.text().strip())

            # 获取演员和导演信息
            actor = ''
            director = ''

            # 从标签中提取演员信息
            tags = doc('.post-tags a, .entry-tags a')
            for tag in tags.items():
                tag_text = tag.text().strip()
                if tag_text and len(tag_text) > 1:
                    if not actor:
                        actor = tag_text
                    else:
                        actor += f", {tag_text}"

            # 播放源和播放列表（这个网站可能需要JavaScript加载，暂时返回空）
            play_from = ''
            play_url = ''

            video_info = {
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'vod_actor': actor,
                'vod_director': director,
                'vod_content': content,
                'vod_play_from': play_from,
                'vod_play_url': play_url,
                'vod_year': '',
                'vod_area': '',
                'vod_lang': '',
                'vod_remarks': ''
            }

            self.log(f"获取视频详情: {title}")
            return {'list': [video_info]}

        except Exception as e:
            self.log(f"获取视频详情失败: {e}")
            return {'list': []}

    def playerContent(self, flag, id, vipFlags):
        """播放地址解析"""
        try:
            # 这个网站的播放地址可能需要特殊处理
            # 暂时返回原始地址
            result = {
                'parse': 0,
                'playUrl': '',
                'url': id
            }

            self.log(f"播放地址解析: {id}")
            return result

        except Exception as e:
            self.log(f"播放地址解析失败: {e}")
            return {'parse': 0, 'playUrl': '', 'url': ''}
