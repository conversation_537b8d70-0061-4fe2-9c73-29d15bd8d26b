# NO视频插件 (novipnoad.py)

## 插件概述

NO视频插件是为PyramidStore项目开发的视频源插件，用于从NO视频网站 (https://www.novipnoad.net/) 获取视频内容。该插件严格遵循PyramidStore开发指南的规范和最佳实践。

## 功能特性

### 核心功能
- **分类浏览**: 支持13个视频分类（电影、动画、综艺、音乐、短片、其他、港剧、台剧、欧美剧、日剧、韩剧、泰剧、土耳其剧）
- **首页推荐**: 获取网站首页推荐视频内容
- **分类列表**: 支持分页浏览各分类下的视频
- **搜索功能**: 支持关键词搜索视频内容
- **详情页面**: 获取视频详细信息、演员、简介等
- **播放解析**: 支持播放地址解析（可扩展）

### 技术特性
- **编码修复**: 自动修复UTF-8编码问题，确保中文内容正确显示
- **数据去重**: 使用set()机制避免重复视频项，提高数据质量
- **图片懒加载**: 智能处理图片懒加载，优先获取真实图片URL
- **智能选择器**: 多种CSS选择器策略，提高数据获取成功率
- **错误处理**: 完善的异常处理和日志记录机制
- **网络容错**: SSL错误处理和网络超时处理
- **Fallback机制**: 网站无法访问时提供备用内容

## 技术架构

### 类结构
```python
class Spider(Spider):  # 继承自base.spider.Spider
    - init()              # 初始化方法
    - getName()           # 获取插件名称
    - homeContent()       # 首页内容
    - categoryContent()   # 分类内容
    - searchContent()     # 搜索功能
    - detailContent()     # 详情页面
    - playerContent()     # 播放解析
```

### 核心方法

#### 编码处理
- `fix_encoding(text)`: 修复Latin1->UTF-8编码问题
- `fetch_with_encoding(url)`: 统一请求方法，确保编码一致性

#### 数据提取
- `extract_video_id(url)`: 从URL中提取视频ID
- `get_video_title(link_elem)`: 多种方式获取视频标题
- `get_video_pic(link_elem)`: 处理图片懒加载，获取真实图片URL

#### 容错机制
- `_get_fallback_home_content()`: 网站无法访问时的备用内容

## 配置说明

### 网站信息
- **目标网站**: https://www.novipnoad.net/
- **插件名称**: NO视频
- **编码格式**: UTF-8
- **请求超时**: 30秒

### 分类映射
| 分类名称 | 分类ID | URL路径 |
|---------|--------|---------|
| 电影 | movie | /movie/ |
| 动画 | anime | /anime/ |
| 综艺 | shows | /shows/ |
| 音乐 | music | /music/ |
| 短片 | short | /short/ |
| 其他 | other | /other/ |
| 港剧 | tv_hongkong | /tv/hongkong/ |
| 台剧 | tv_taiwan | /tv/taiwan/ |
| 欧美剧 | tv_western | /tv/western/ |
| 日剧 | tv_japan | /tv/japan/ |
| 韩剧 | tv_korea | /tv/korea/ |
| 泰剧 | tv_thailand | /tv/thailand/ |
| 土耳其剧 | tv_turkey | /tv/turkey/ |

### URL模式
- **首页**: https://www.novipnoad.net/
- **分类页**: https://www.novipnoad.net/{category}/
- **分页**: https://www.novipnoad.net/{category}/page/{page}/
- **搜索**: https://www.novipnoad.net/?s={keyword}
- **详情页**: https://www.novipnoad.net/{category}/{id}.html

## 使用方法

### 基本使用
```python
from novipnoad import Spider

# 创建插件实例
spider = Spider()
spider.init()

# 获取首页内容
home_result = spider.homeContent({})
classes = home_result['class']  # 分类列表
videos = home_result['list']    # 推荐视频

# 获取分类内容
category_result = spider.categoryContent('movie', '1', {}, {})
category_videos = category_result['list']

# 搜索视频
search_result = spider.searchContent('疾速', False, '1')
search_videos = search_result['list']

# 获取视频详情
detail_result = spider.detailContent(['150633'])
video_detail = detail_result['list'][0]
```

### 数据格式

#### 视频项格式
```python
{
    'vod_id': '150633',           # 视频ID
    'vod_name': '示例电影',        # 视频标题
    'vod_pic': 'https://...',     # 封面图片
    'vod_year': '2024',           # 年份
    'vod_remarks': '高清',        # 备注
    'vod_content': '简介...',     # 简介（搜索结果中）
}
```

#### 详情页格式
```python
{
    'vod_id': '150633',
    'vod_name': '示例电影',
    'vod_pic': 'https://...',
    'vod_actor': '演员1, 演员2',
    'vod_director': '导演',
    'vod_content': '详细简介',
    'vod_play_from': '播放源',
    'vod_play_url': '播放列表',
    'vod_year': '2024',
    'vod_area': '地区',
    'vod_lang': '语言',
    'vod_remarks': '备注'
}
```

## 测试验证

### 运行测试
```bash
cd plugin/html
python novipnoad_test.py
```

### 测试覆盖
- ✅ 基础功能测试（所有核心方法）
- ✅ 数据质量检查（去重、编码、图片URL）
- ✅ 错误处理测试
- ✅ 网络容错测试

## 开发规范遵循

### 代码质量
- 遵循PyramidStore开发指南的代码结构
- 实现所有标准接口方法
- 添加详细的中文注释和文档字符串
- 使用统一的命名约定

### 依赖管理
- 仅使用项目允许的依赖模块
- 避免引入额外的第三方库
- 使用项目标准的requests和pyquery

### 错误处理
- 每个方法都有完善的异常处理
- 详细的日志记录便于调试
- 网络错误的优雅降级

## 注意事项

### 网站状态
- 目标网站可能存在SSL证书问题
- 内容可能通过JavaScript动态加载
- 建议定期检查网站结构变化

### 性能优化
- 实现了数据去重机制
- 限制首页视频数量（20个）
- 使用智能选择器减少无效请求

### 扩展性
- 插件架构支持轻松扩展新功能
- 选择器策略可根据网站变化调整
- 播放解析方法可根据需要完善

## 维护更新

### 常见问题
1. **SSL错误**: 插件已配置禁用SSL验证
2. **编码问题**: 自动修复Latin1->UTF-8转换
3. **网站无法访问**: 自动使用fallback内容

### 更新建议
- 定期检查目标网站的HTML结构变化
- 根据网站更新调整CSS选择器
- 监控网站的反爬虫策略变化

---

**开发者**: Augment Agent  
**开发时间**: 2025-08-05  
**版本**: 1.0.0  
**遵循规范**: PyramidStore开发指南
