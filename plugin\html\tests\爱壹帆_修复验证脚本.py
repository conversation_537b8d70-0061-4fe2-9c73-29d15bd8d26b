# -*- coding: utf-8 -*-
# 爱壹帆插件修复验证脚本
import sys
import json
sys.path.append('..')

def test_fixes():
    """验证插件修复效果"""

    print("=" * 60)
    print("爱壹帆插件修复验证测试")
    print("=" * 60)

    try:
        # 导入插件
        from 爱壹帆 import Spider
        spider = Spider()
        spider.init()

        print(f"插件名称: {spider.getName()}")
        print(f"网站地址: {spider.host}")

        # 测试1: 首页去重验证
        print("\n1. 首页去重验证")
        print("-" * 30)
        try:
            home_result = spider.homeContent({})
            videos = home_result.get('list', [])

            # 检查是否有重复的视频ID
            video_ids = [v['vod_id'] for v in videos]
            unique_ids = set(video_ids)

            print(f"首页视频总数: {len(videos)}")
            print(f"唯一视频ID数: {len(unique_ids)}")
            print(f"重复视频数: {len(video_ids) - len(unique_ids)}")

            if len(video_ids) == len(unique_ids):
                print("✅ 首页去重测试通过")
            else:
                print("❌ 首页仍存在重复视频")
                # 找出重复的ID
                from collections import Counter
                id_counts = Counter(video_ids)
                duplicates = [vid for vid, count in id_counts.items() if count > 1]
                print(f"重复的视频ID: {duplicates}")

        except Exception as e:
            print(f"首页去重测试失败: {e}")

        # 测试2: 搜索结果验证
        print("\n2. 搜索结果验证")
        print("-" * 30)
        try:
            search_result = spider.searchContent('枪口', False, '1')
            search_videos = search_result.get('list', [])

            print(f"搜索结果数量: {len(search_videos)}")

            if search_videos:
                # 检查搜索结果的质量
                for i, video in enumerate(search_videos[:3]):
                    print(f"搜索结果 {i+1}:")
                    print(f"  ID: {video['vod_id']}")
                    print(f"  标题: {video['vod_name']}")
                    print(f"  备注: {video['vod_remarks']}")

                    # 检查标题是否合理（不应该是"正片"、"详情"等）
                    if video['vod_name'] in ['正片', '详情', '播放', '观看']:
                        print(f"  ❌ 标题异常: {video['vod_name']}")
                    else:
                        print(f"  ✅ 标题正常")

                # 检查是否有重复
                search_ids = [v['vod_id'] for v in search_videos]
                unique_search_ids = set(search_ids)
                if len(search_ids) == len(unique_search_ids):
                    print("✅ 搜索结果去重测试通过")
                else:
                    print("❌ 搜索结果仍存在重复")
            else:
                print("❌ 搜索无结果")

        except Exception as e:
            print(f"搜索结果测试失败: {e}")

        # 测试3: 详情页编码验证
        print("\n3. 详情页编码验证")
        print("-" * 30)
        try:
            # 使用已知的视频ID进行测试
            test_video_id = "84741"  # 枪口彼端
            detail_result = spider.detailContent([test_video_id])

            if detail_result.get('list'):
                detail = detail_result['list'][0]
                title = detail.get('vod_name', '')
                content = detail.get('vod_content', '')

                print(f"视频标题: {title}")
                print(f"视频简介: {content[:100]}...")

                # 检查编码问题
                def has_encoding_issues(text):
                    """检查文本是否有编码问题"""
                    if not text:
                        return False
                    # 检查常见乱码特征
                    garbled_patterns = [
                        '\u00e4\u00b8', '\u00e5', '\u00e6', '\u00e7',
                        '\u00c3\u00a4', '\u00c3\u00a5', '\u00c3\u00a6'
                    ]
                    return any(pattern in text for pattern in garbled_patterns)

                title_has_issues = has_encoding_issues(title)
                content_has_issues = has_encoding_issues(content)

                if not title_has_issues and not content_has_issues:
                    print("✅ 编码测试通过")
                else:
                    print("❌ 仍存在编码问题")
                    if title_has_issues:
                        print(f"  标题编码异常: {title}")
                    if content_has_issues:
                        print(f"  简介编码异常: {content[:50]}...")

            else:
                print("❌ 无法获取详情信息")

        except Exception as e:
            print(f"详情页编码测试失败: {e}")

        # 测试4: 分类页去重验证
        print("\n4. 分类页去重验证")
        print("-" * 30)
        try:
            category_result = spider.categoryContent('1', '1', {}, {})  # 电影分类
            category_videos = category_result.get('list', [])

            # 检查是否有重复的视频ID
            category_ids = [v['vod_id'] for v in category_videos]
            unique_category_ids = set(category_ids)

            print(f"分类页视频总数: {len(category_videos)}")
            print(f"唯一视频ID数: {len(unique_category_ids)}")
            print(f"重复视频数: {len(category_ids) - len(unique_category_ids)}")

            if len(category_ids) == len(unique_category_ids):
                print("✅ 分类页去重测试通过")
            else:
                print("❌ 分类页仍存在重复视频")

        except Exception as e:
            print(f"分类页去重测试失败: {e}")

        print("\n" + "=" * 60)
        print("修复验证测试完成")
        print("=" * 60)

    except ImportError as e:
        print(f"导入插件失败: {e}")
        print("请确保插件文件存在且语法正确")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_fixes()