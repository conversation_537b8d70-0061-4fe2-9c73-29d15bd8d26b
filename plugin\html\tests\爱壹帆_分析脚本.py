# -*- coding: utf-8 -*-
# 爱壹帆网站结构分析脚本
import requests
from pyquery import PyQuery as pq
import json
import re
from urllib.parse import quote

def analyze_website():
    """分析爱壹帆网站结构"""

    host = 'https://www.iyf.lv'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    print("=" * 60)
    print("爱壹帆网站结构分析报告")
    print("=" * 60)

    # 1. 分析首页
    print("\n1. 首页分析")
    print("-" * 30)
    try:
        response = requests.get(host, headers=headers, timeout=10)
        response.encoding = 'utf-8'
        doc = pq(response.text)

        print(f"状态码: {response.status_code}")
        print(f"页面标题: {doc('title').text()}")

        # 分析导航分类
        nav_items = doc('.navbar a, nav a, .nav a')
        print(f"\n导航链接数量: {len(nav_items)}")
        categories = []
        for item in nav_items.items():
            text = item.text().strip()
            href = item.attr('href')
            if text and href and '/t/' in href:
                categories.append({'name': text, 'url': href})
                print(f"  分类: {text} -> {href}")

        # 分析视频列表容器
        video_selectors = [
            '.module-item', '.video-item', '.movie-item', '.card',
            '.list-item', '.item', '.video-card', '.movie-card'
        ]

        video_container = None
        for selector in video_selectors:
            items = doc(selector)
            if items:
                video_container = selector
                print(f"\n视频容器选择器: {selector} (找到 {len(items)} 个)")

                # 分析第一个视频项的结构
                if len(items) > 0:
                    first_item = items.eq(0)
                    print(f"第一个视频项HTML: {first_item.outer_html()[:200]}...")

                    # 分析标题获取方式
                    title_methods = [
                        ('title属性', first_item.attr('title')),
                        ('文本内容', first_item.text()),
                        ('img alt', first_item.find('img').attr('alt')),
                        ('a title', first_item.find('a').attr('title')),
                    ]

                    for method, value in title_methods:
                        if value:
                            print(f"  标题获取方式 - {method}: {value[:50]}...")

                    # 分析链接获取方式
                    href_methods = [
                        ('自身href', first_item.attr('href')),
                        ('内部a标签href', first_item.find('a').attr('href')),
                    ]

                    for method, value in href_methods:
                        if value:
                            print(f"  链接获取方式 - {method}: {value}")

                    # 分析图片获取方式
                    img_elem = first_item.find('img')
                    if img_elem:
                        img_methods = [
                            ('data-original', img_elem.attr('data-original')),
                            ('data-src', img_elem.attr('data-src')),
                            ('src', img_elem.attr('src')),
                        ]

                        for method, value in img_methods:
                            if value:
                                print(f"  图片获取方式 - {method}: {value[:50]}...")
                break

        if not video_container:
            print("\n未找到明显的视频容器，尝试其他方法...")
            # 查找包含链接的元素
            links = doc('a[href*="/iyftv/"]')
            if links:
                print(f"找到 {len(links)} 个视频链接")
                first_link = links.eq(0)
                print(f"第一个链接: {first_link.attr('href')}")
                print(f"链接文本: {first_link.text()}")

    except Exception as e:
        print(f"首页分析失败: {e}")

    # 2. 分析分类页
    print(f"\n2. 分类页分析 (电影分类)")
    print("-" * 30)
    try:
        category_url = f"{host}/t/1/"
        response = requests.get(category_url, headers=headers, timeout=10)
        response.encoding = 'utf-8'
        doc = pq(response.text)

        print(f"状态码: {response.status_code}")
        print(f"页面标题: {doc('title').text()}")

        # 查找视频列表
        if video_container:
            items = doc(video_container)
            print(f"分类页视频数量: {len(items)}")
        else:
            # 尝试查找视频链接
            links = doc('a[href*="/iyftv/"]')
            print(f"分类页视频链接数量: {len(links)}")

    except Exception as e:
        print(f"分类页分析失败: {e}")

    # 3. 分析搜索页
    print(f"\n3. 搜索页分析")
    print("-" * 30)
    try:
        # 测试搜索URL格式
        search_patterns = [
            f"{host}/search?wd={quote('测试')}",
            f"{host}/s/?wd={quote('测试')}",
            f"{host}/s/-------------/?wd={quote('测试')}",
        ]

        for pattern in search_patterns:
            try:
                response = requests.get(pattern, headers=headers, timeout=10)
                if response.status_code == 200:
                    print(f"有效搜索URL: {pattern}")
                    response.encoding = 'utf-8'
                    doc = pq(response.text)
                    print(f"搜索页标题: {doc('title').text()}")

                    # 分析搜索结果
                    if video_container:
                        items = doc(video_container)
                        print(f"搜索结果数量: {len(items)}")
                    else:
                        links = doc('a[href*="/iyftv/"]')
                        print(f"搜索结果链接数量: {len(links)}")
                    break
            except:
                continue

    except Exception as e:
        print(f"搜索页分析失败: {e}")

    # 4. 分析详情页
    print(f"\n4. 详情页分析")
    print("-" * 30)
    try:
        detail_url = f"{host}/iyftv/84741/"  # 使用之前看到的视频ID
        response = requests.get(detail_url, headers=headers, timeout=10)
        response.encoding = 'utf-8'
        doc = pq(response.text)

        print(f"状态码: {response.status_code}")
        print(f"页面标题: {doc('title').text()}")

        # 分析标题
        title_selectors = ['h1', '.title', '.video-title', '.movie-title']
        for selector in title_selectors:
            title_elem = doc(selector)
            if title_elem:
                print(f"标题选择器: {selector} -> {title_elem.text()}")
                break

        # 分析播放源
        tab_selectors = ['.module-tab-item', '.tab-item', '.play-source', '.source-tab']
        for selector in tab_selectors:
            tabs = doc(selector)
            if tabs:
                print(f"播放源选择器: {selector} (找到 {len(tabs)} 个)")
                for i, tab in enumerate(tabs.items()):
                    print(f"  播放源 {i+1}: {tab.text()}")
                break

        # 分析播放列表
        playlist_selectors = ['.module-play-list', '.play-list', '.episode-list']
        for selector in playlist_selectors:
            playlists = doc(selector)
            if playlists:
                print(f"播放列表选择器: {selector} (找到 {len(playlists)} 个)")
                if len(playlists) > 0:
                    first_playlist = playlists.eq(0)
                    episodes = first_playlist.find('a')
                    print(f"  第一个播放列表集数: {len(episodes)}")
                break

    except Exception as e:
        print(f"详情页分析失败: {e}")

    print("\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)

if __name__ == "__main__":
    analyze_website()