# 爱壹帆插件

## 插件信息

- **插件名称**: 爱壹帆
- **网站地址**: https://www.iyf.lv/
- **插件类型**: 影视资源采集插件
- **开发状态**: 已完成
- **兼容性**: PyramidStore框架

## 功能特性

### 支持的功能
- ✅ 首页内容获取
- ✅ 分类浏览 (电影、剧集、综艺、动漫)
- ✅ 视频详情获取
- ✅ 搜索功能
- ✅ 播放地址解析
- ✅ 多播放源支持
- ✅ 编码问题自动修复
- ✅ 图片懒加载处理

### 支持的分类
- 电影 (分类ID: 1)
- 剧集 (分类ID: 2)
- 综艺 (分类ID: 3)
- 动漫 (分类ID: 4)

## 安装使用

### 1. 环境要求
- Python 3.6+
- PyramidStore框架
- 依赖库: requests, lxml, pyquery

### 2. 安装步骤
1. 将 `爱壹帆.py` 文件放入 `plugin/html/` 目录
2. 确保依赖库已安装:
   ```bash
   pip install requests lxml pyquery
   ```

### 3. 测试插件
运行测试脚本验证插件功能:
```bash
cd plugin/html
python 爱壹帆_测试脚本.py
```

## 技术实现

### URL模式
- 首页: `/`
- 分类: `/t/{分类ID}/`
- 分页: `/t/{分类ID}/page/{页码}/`
- 详情: `/iyftv/{视频ID}/`
- 搜索: `/s/-------------/?wd={关键词}`
- 播放: `/iyfplay/{视频ID}-{播放源}-{集数}/`

### 数据解析策略
- **视频列表**: 查找包含 `/iyftv/` 的链接元素
- **标题获取**: 优先从链接文本，其次从图片alt属性
- **图片处理**: 支持懒加载，检查 `data-original`、`data-src`、`src` 属性
- **播放源**: 解析播放源选择器和对应的集数列表

### 编码处理
- 自动检测UTF-8编码问题
- 支持Latin1->UTF-8转换
- 智能乱码修复机制

## 开发说明

### 核心方法
- `homeContent()`: 获取首页分类和推荐视频
- `categoryContent()`: 获取分类视频列表
- `detailContent()`: 获取视频详情和播放源
- `searchContent()`: 搜索视频内容
- `playerContent()`: 解析播放地址

### 辅助方法
- `fix_encoding()`: 编码修复
- `fetch_with_encoding()`: 带编码处理的请求
- `getpq()`: 安全的pyquery解析

## 注意事项

1. **网络访问**: 插件需要访问外部网站，请确保网络连接正常
2. **编码问题**: 已内置编码修复机制，如遇问题会自动尝试修复
3. **错误处理**: 所有方法都包含完善的异常处理机制
4. **日志记录**: 使用 `self.log()` 记录关键信息和错误

## 更新日志

### v1.0.0 (2025-08-05)
- 初始版本发布
- 实现所有基础功能
- 完善错误处理和编码修复
- 添加测试脚本和文档

## 开发者信息

- 基于PyramidStore开发指南开发
- 参考泥视频插件的成功经验
- 严格遵循项目编码规范

## 许可证

本插件遵循PyramidStore项目的许可证协议。