# -*- coding: utf-8 -*-
# 爱壹帆插件综合测试脚本
import sys
import json
from collections import Counter
sys.path.append('..')

def test_basic_functions():
    """测试基础功能"""
    print("=" * 60)
    print("爱壹帆插件基础功能测试")
    print("=" * 60)

    try:
        # 导入插件
        from 爱壹帆 import Spider
        spider = Spider()
        spider.init()

        print(f"插件名称: {spider.getName()}")
        print(f"网站地址: {spider.host}")

        # 测试1: 首页内容
        print("\n1. 测试首页内容")
        print("-" * 30)
        try:
            home_result = spider.homeContent({})
            classes = home_result.get('class', [])
            videos = home_result.get('list', [])

            print(f"分类数量: {len(classes)}")
            if classes:
                print("分类列表:")
                for cls in classes[:5]:  # 显示前5个分类
                    print(f"  - {cls['type_name']} (ID: {cls['type_id']})")

            print(f"首页视频数量: {len(videos)}")
            if videos:
                print("首页视频示例:")
                for video in videos[:3]:  # 显示前3个视频
                    print(f"  - {video['vod_name']} (ID: {video['vod_id']})")
                    if video['vod_remarks']:
                        print(f"    备注: {video['vod_remarks']}")

            return classes, videos
        except Exception as e:
            print(f"首页测试失败: {e}")
            return [], []

    except ImportError as e:
        print(f"导入插件失败: {e}")
        return [], []

def test_quality_checks():
    """测试数据质量（去重、编码等）"""
    print("\n" + "=" * 60)
    print("数据质量检查测试")
    print("=" * 60)

    try:
        from 爱壹帆 import Spider
        spider = Spider()
        spider.init()

        # 测试首页去重
        print("\n1. 首页去重检查")
        print("-" * 30)
        home_result = spider.homeContent({})
        videos = home_result.get('list', [])

        video_ids = [v['vod_id'] for v in videos]
        unique_ids = set(video_ids)

        print(f"首页视频总数: {len(videos)}")
        print(f"唯一视频ID数: {len(unique_ids)}")
        print(f"重复视频数: {len(video_ids) - len(unique_ids)}")

        if len(video_ids) == len(unique_ids):
            print("✅ 首页去重测试通过")
        else:
            print("❌ 首页仍存在重复视频")
            id_counts = Counter(video_ids)
            duplicates = [vid for vid, count in id_counts.items() if count > 1]
            print(f"重复的视频ID: {duplicates}")

        # 测试搜索结果质量
        print("\n2. 搜索结果质量检查")
        print("-" * 30)
        search_result = spider.searchContent('枪口', False, '1')
        search_videos = search_result.get('list', [])

        print(f"搜索结果数量: {len(search_videos)}")

        if search_videos:
            # 检查标题质量
            invalid_titles = []
            for video in search_videos:
                if video['vod_name'] in ['正片', '详情', '播放', '观看']:
                    invalid_titles.append(video['vod_name'])

            if not invalid_titles:
                print("✅ 搜索结果标题质量检查通过")
            else:
                print(f"❌ 发现无效标题: {invalid_titles}")

            # 检查去重
            search_ids = [v['vod_id'] for v in search_videos]
            unique_search_ids = set(search_ids)
            if len(search_ids) == len(unique_search_ids):
                print("✅ 搜索结果去重检查通过")
            else:
                print("❌ 搜索结果存在重复")

        # 测试编码问题
        print("\n3. 编码问题检查")
        print("-" * 30)
        test_video_id = "84741"  # 枪口彼端
        detail_result = spider.detailContent([test_video_id])

        if detail_result.get('list'):
            detail = detail_result['list'][0]
            title = detail.get('vod_name', '')
            content = detail.get('vod_content', '')

            # 检查编码问题
            def has_encoding_issues(text):
                if not text:
                    return False
                garbled_patterns = [
                    '\u00e4\u00b8', '\u00e5', '\u00e6', '\u00e7',
                    '\u00c3\u00a4', '\u00c3\u00a5', '\u00c3\u00a6'
                ]
                return any(pattern in text for pattern in garbled_patterns)

            title_has_issues = has_encoding_issues(title)
            content_has_issues = has_encoding_issues(content)

            if not title_has_issues and not content_has_issues:
                print("✅ 编码检查通过")
                print(f"标题: {title}")
            else:
                print("❌ 仍存在编码问题")
                if title_has_issues:
                    print(f"标题编码异常: {title}")
                if content_has_issues:
                    print(f"简介编码异常: {content[:50]}...")

    except Exception as e:
        print(f"质量检查测试失败: {e}")

def test_all_functions():
    """完整功能测试"""
    print("\n" + "=" * 60)
    print("完整功能测试")
    print("=" * 60)

    try:
        from 爱壹帆 import Spider
        spider = Spider()
        spider.init()

        # 获取基础数据
        home_result = spider.homeContent({})
        classes = home_result.get('class', [])
        videos = home_result.get('list', [])

        # 测试分类内容
        print("\n1. 分类内容测试")
        print("-" * 30)
        if classes:
            category_result = spider.categoryContent(classes[0]['type_id'], '1', {}, {})
            category_videos = category_result.get('list', [])
            print(f"分类视频数量: {len(category_videos)}")

        # 测试详情页面
        print("\n2. 详情页面测试")
        print("-" * 30)
        if videos:
            test_video_id = videos[0]['vod_id']
            detail_result = spider.detailContent([test_video_id])

            if detail_result.get('list'):
                detail = detail_result['list'][0]
                print(f"视频标题: {detail.get('vod_name', '')}")

                play_from = detail.get('vod_play_from', '')
                if play_from:
                    sources = play_from.split('$$$')
                    print(f"播放源数量: {len(sources)}")

        # 测试播放地址
        print("\n3. 播放地址测试")
        print("-" * 30)
        test_play_url = "/iyfplay/84741-1-1/"
        player_result = spider.playerContent("", test_play_url, [])
        print(f"解析模式: {player_result.get('parse', '')}")

        print("\n✅ 所有功能测试完成")

    except Exception as e:
        print(f"功能测试失败: {e}")

if __name__ == "__main__":
    # 运行基础功能测试
    classes, videos = test_basic_functions()

    # 运行质量检查测试
    test_quality_checks()

    # 运行完整功能测试
    test_all_functions()

    print("\n" + "=" * 60)
    print("所有测试完成")
    print("=" * 60)