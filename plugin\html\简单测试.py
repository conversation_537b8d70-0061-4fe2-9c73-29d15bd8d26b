# -*- coding: utf-8 -*-
# 简单测试脚本
import sys
import os

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

try:
    print("开始测试...")
    
    # 测试导入
    print("1. 测试导入...")
    from kvm4k import Spider
    print("✅ 导入成功")
    
    # 测试实例化
    print("2. 测试实例化...")
    spider = Spider()
    print("✅ 实例化成功")
    
    # 测试基本属性
    print("3. 测试基本属性...")
    print(f"   插件名称: {spider.getName()}")
    print(f"   网站地址: {spider.host}")
    print("✅ 基本属性正常")
    
    # 测试编码修复
    print("4. 测试编码修复...")
    test_text = "测试文本"
    result = spider.fix_encoding(test_text)
    print(f"   输入: {test_text} -> 输出: {result}")
    print("✅ 编码修复正常")
    
    print("\n测试完成！插件基础功能正常。")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
